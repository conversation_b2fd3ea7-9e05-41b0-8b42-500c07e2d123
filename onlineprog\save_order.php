<?php
// تمكين عرض الأخطاء للتصحيح
error_reporting(E_ALL);
ini_set('display_errors', 1);

// إنشاء ملف تخزين الطلبيات إذا لم يكن موجودًا
$ordersFile = 'orders_data.json';

// تحميل البيانات الحالية إذا كانت موجودة
$ordersData = [];
if (file_exists($ordersFile)) {
    $fileContent = file_get_contents($ordersFile);
    if (!empty($fileContent)) {
        $ordersData = json_decode($fileContent, true);
        if ($ordersData === null) {
            // خطأ في تحليل JSON
            $ordersData = [];
        }
    }
}

// التحقق من وجود البيانات المرسلة
if (isset($_POST['order_data'])) {
    $orderData = json_decode($_POST['order_data'], true);
    
    // إضافة معلومات إضافية للطلبية
    $orderData['order_id'] = time(); // استخدام الوقت الحالي كمعرف فريد للطلبية
    $orderData['order_date'] = date('Y-m-d H:i:s'); // تاريخ ووقت إنشاء الطلبية
    
    // إضافة الطلبية الجديدة إلى المصفوفة
    $ordersData[] = $orderData;
    
    // حفظ البيانات في ملف JSON
    $result = file_put_contents($ordersFile, json_encode($ordersData, JSON_PRETTY_PRINT));
    
    if ($result !== false) {
        // إرجاع معرف الطلبية
        echo json_encode(['success' => true, 'order_id' => $orderData['order_id']]);
    } else {
        // فشل في الكتابة على الملف
        echo json_encode(['success' => false, 'message' => 'فشل في الكتابة على ملف الطلبيات']);
    }
} else {
    // إرجاع رسالة خطأ
    echo json_encode(['success' => false, 'message' => 'لم يتم استلام بيانات الطلبية']);
}
?>