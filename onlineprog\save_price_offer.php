<?php
// تمكين عرض الأخطاء للتصحيح
error_reporting(E_ALL);
ini_set('display_errors', 1);

// إنشاء ملف تخزين عروض الأسعار إذا لم يكن موجودًا
$offersFile = 'price_offers_data.json';

// تحميل البيانات الحالية إذا كانت موجودة
$offersData = [];
if (file_exists($offersFile)) {
    $fileContent = file_get_contents($offersFile);
    if (!empty($fileContent)) {
        $offersData = json_decode($fileContent, true);
        if ($offersData === null) {
            // خطأ في تحليل JSON
            $offersData = [];
        }
    }
}

// التحقق من وجود البيانات المرسلة
if (isset($_POST['offer_data'])) {
    $offerData = json_decode($_POST['offer_data'], true);
    
    // إضافة معلومات إضافية للعرض
    $offerData['offer_id'] = time(); // استخدام الوقت الحالي كمعرف فريد للعرض
    $offerData['offer_date'] = date('Y-m-d H:i:s'); // تاريخ ووقت إنشاء العرض
    
    // إضافة العرض الجديد إلى المصفوفة
    $offersData[] = $offerData;
    
    // حفظ البيانات في ملف JSON
    $result = file_put_contents($offersFile, json_encode($offersData, JSON_PRETTY_PRINT));
    
    if ($result !== false) {
        // إرجاع معرف العرض
        echo json_encode(['success' => true, 'offer_id' => $offerData['offer_id']]);
    } else {
        // فشل في الكتابة على الملف
        echo json_encode(['success' => false, 'message' => 'فشل في الكتابة على ملف عروض الأسعار']);
    }
} else {
    // إرجاع رسالة خطأ
    echo json_encode(['success' => false, 'message' => 'لم يتم استلام بيانات العرض']);
}
?>