<?php
session_start();

// التحقق من وجود سلة التسوق في الجلسة
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

// التحقق من وجود معرف المنتج في الطلب
if (!isset($_GET['id'])) {
    header('Location: customer_products.php');
    exit;
}

$productId = $_GET['id'];

// تحميل بيانات المنتجات
$productsData = [];
$dataFile = 'products_data.json';
if (file_exists($dataFile)) {
    $productsData = json_decode(file_get_contents($dataFile), true);
}

// التحقق من وجود المنتج
if (!isset($productsData[$productId])) {
    header('Location: customer_products.php');
    exit;
}

$product = $productsData[$productId];
$product['id'] = $productId;

// تحميل إعدادات الموقع
$settingsFile = 'site_settings.json';
$siteSettings = [];

// التحقق من وجود ملف الإعدادات
if (file_exists($settingsFile)) {
    $siteSettings = json_decode(file_get_contents($settingsFile), true);
} else {
    // إعدادات افتراضية
    $siteSettings = [
        'site' => [
            'title' => 'متجر القرطاسية - تفاصيل المنتج',
            'footer_text' => 'جميع الحقوق محفوظة - متجر القرطاسية',
            'logo' => 'default-logo.png'
        ]
    ];
}

// إضافة منتج إلى سلة التسوق
if (isset($_POST['add_to_cart'])) {
    $productId = $_POST['product_id'];
    $productName = $_POST['product_name'];
    $productPrice = $_POST['product_price'];
    $productImage = $_POST['product_image'];
    $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;
    $unit = isset($_POST['unit']) ? $_POST['unit'] : 'قطعة';
    $selectedColor = isset($_POST['color']) ? $_POST['color'] : '';
    
    // حساب السعر الإجمالي بناءً على الكمية ووحدة القياس
    $totalPrice = calculateTotalPrice($productPrice, $quantity, $unit, $productsData[$productId]);
    
    // إضافة المنتج إلى السلة
    $item = [
        'id' => $productId,
        'name' => $productName,
        'price' => $productPrice,
        'image' => $productImage,
        'quantity' => $quantity,
        'unit' => $unit,
        'color' => $selectedColor,
        'total_price' => $totalPrice
    ];
    
    $_SESSION['cart'][] = $item;
    
    // إعادة التوجيه لتجنب إعادة إرسال النموذج
    header('Location: product_details.php?id=' . $productId . '&added=1');
    exit;
}

// دالة لحساب السعر الإجمالي بناءً على الكمية ووحدة القياس
function calculateTotalPrice($price, $quantity, $unit, $product) {
    $totalPrice = $price * $quantity;
    
    // تعديل السعر بناءً على وحدة القياس
    if ($unit == 'كرتون') {
        // استخدام قيمة الكمية من المنتج إذا كانت موجودة، وإلا استخدام القيمة الافتراضية 100
        $cartonQuantity = isset($product['quantity']) ? $product['quantity'] : 100;
        $totalPrice = $price * $quantity * $cartonQuantity;
    } elseif ($unit == 'دزينة') {
        // الدزينة تحتوي على 12 قطعة
        $totalPrice = $price * $quantity * 12;
    } elseif ($unit == 'علبة') {
        // نفترض أن العلبة تحتوي على 10 قطع
        $totalPrice = $price * $quantity * 10;
    }
    
    return $totalPrice;
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $product['name']; ?> - <?php echo isset($siteSettings['site']['title']) ? $siteSettings['site']['title'] : 'متجر القرطاسية'; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 0;
            margin: 0;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem 0;
            text-align: center;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.8rem 1rem;
        }
        
        .header-menu-icon {
            font-size: 1.5rem;
            cursor: pointer;
        }
        
        .header-logo {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .header-logo img {
            height: 40px;
            width: auto;
        }
        
        .header-logo h1 {
            font-size: 1.5rem;
            margin: 0;
        }
        
        .header-user {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .header-user img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px 50px;
        }
        
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            margin-bottom: 20px;
        }
        
        .nav-links {
            display: flex;
            gap: 15px;
        }
        
        .nav-link {
            text-decoration: none;
            color: var(--dark-color);
            font-weight: 500;
            padding: 8px 12px;
            border-radius: var(--border-radius);
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .nav-link:hover {
            background-color: var(--light-color);
        }
        
        .nav-link.active {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .cart-icon {
            position: relative;
        }
        
        .cart-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: var(--accent-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: bold;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            background-color: var(--success-color);
            color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            z-index: 1000;
            animation: fadeOut 3s forwards;
        }
        
        @keyframes fadeOut {
            0% { opacity: 1; }
            70% { opacity: 1; }
            100% { opacity: 0; visibility: hidden; }
        }
        
        .footer {
            background-color: var(--dark-color);
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 50px;
        }
        
        /* تصميم صفحة تفاصيل المنتج */
        .product-details {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
            margin-bottom: 30px;
        }
        
        .product-details-container {
            display: flex;
            flex-direction: row;
        }
        
        @media (max-width: 768px) {
            .product-details-container {
                flex-direction: column;
            }
        }
        
        .product-image-container {
            flex: 1;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f9f9f9;
        }
        
        .product-image-large {
            width: 100%;
            max-height: 400px;
            object-fit: contain;
            border-radius: var(--border-radius);
            transition: transform 0.3s ease;
        }
        
        .product-image-large:hover {
            transform: scale(1.05);
        }
        
        .product-info-container {
            flex: 1;
            padding: 30px;
            display: flex;
            flex-direction: column;
        }
        
        .product-name-large {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 15px;
        }
        
        .product-price-large {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 20px;
        }
        
        .product-description {
            margin-bottom: 20px;
            line-height: 1.8;
            color: #555;
        }
        
        .product-meta {
            margin-bottom: 20px;
        }
        
        .product-meta-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .meta-label {
            font-weight: 600;
            margin-left: 10px;
            min-width: 120px;
        }
        
        .meta-value {
            color: #555;
        }
        
        .product-colors {
            margin-bottom: 20px;
        }
        
        .colors-title {
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .color-options {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .color-option {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            border: 2px solid transparent;
            transition: var(--transition);
        }
        
        .color-option.selected {
            border-color: var(--secondary-color);
            transform: scale(1.1);
        }
        
        .product-form {
            margin-top: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .quantity-input {
            width: 80px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            text-align: center;
            font-size: 1rem;
        }
        
        .unit-select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 1rem;
            min-width: 120px;
        }
        
        .total-price-display {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--accent-color);
            margin: 15px 0;
        }
        
        .add-to-cart-large {
            background-color: var(--secondary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            padding: 12px 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            width: 100%;
            margin-top: 10px;
        }
        
        .add-to-cart-large:hover {
            background-color: #2980b9;
        }
        
        .back-button {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            background-color: var(--light-color);
            color: var(--dark-color);
            border: none;
            border-radius: var(--border-radius);
            padding: 10px 15px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            margin-bottom: 20px;
            text-decoration: none;
        }
        
        .back-button:hover {
            background-color: #ddd;
        }
        
        /* تعديلات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .product-name-large {
                font-size: 1.5rem;
            }
            
            .product-price-large {
                font-size: 1.3rem;
            }
            
            .product-image-container {
                padding: 15px;
            }
            
            .product-info-container {
                padding: 20px;
            }
            
            .product-image-large {
                max-height: 300px;
            }
        }
        
        @media (max-width: 480px) {
            .product-name-large {
                font-size: 1.3rem;
            }
            
            .product-price-large {
                font-size: 1.2rem;
            }
            
            .meta-label {
                min-width: 100px;
            }
            
            .product-image-large {
                max-height: 250px;
            }
            
            .color-option {
                width: 25px;
                height: 25px;
            }
        }
    </style>
</head>
<body>
    <?php if (isset($_GET['added']) && $_GET['added'] == 1): ?>
    <div class="notification">
        <i class="fas fa-check-circle"></i> تمت إضافة المنتج إلى سلة التسوق بنجاح!
    </div>
    <?php endif; ?>

    <div class="header">
        <div class="header-menu-icon" id="header-menu-toggle">
            <i class="fas fa-bars"></i>
        </div>
        
        <div class="header-logo">
            <?php if (isset($siteSettings['site']['logo']) && !empty($siteSettings['site']['logo'])): ?>
                <img src="uploads/<?php echo $siteSettings['site']['logo']; ?>" alt="شعار الموقع">
            <?php else: ?>
                <h1><?php echo isset($siteSettings['site']['title']) ? $siteSettings['site']['title'] : 'متجر القرطاسية'; ?></h1>
            <?php endif; ?>
        </div>
        
        <div class="header-user">
            <?php if (isset($_SESSION['customer_id']) && isset($customerData['profile_image'])): ?>
                <img src="uploads/<?php echo $customerData['profile_image']; ?>" alt="صورة المستخدم">
            <?php else: ?>
                <i class="fas fa-user"></i>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="container">
        <div class="navbar">
            <div class="nav-links">
                <a href="customer.php" class="nav-link"><i class="fas fa-home"></i> الرئيسية</a>
                <a href="customer_products.php" class="nav-link active"><i class="fas fa-shopping-bag"></i> المنتجات</a>
                <a href="customer_cart.php" class="nav-link cart-icon">
                    <i class="fas fa-shopping-cart"></i> سلة التسوق
                    <?php if (count($_SESSION['cart']) > 0): ?>
                    <span class="cart-count"><?php echo count($_SESSION['cart']); ?></span>
                    <?php endif; ?>
                </a>
            </div>
        </div>
        
        <a href="customer_products.php" class="back-button">
            <i class="fas fa-arrow-right"></i> العودة إلى المنتجات
        </a>
        
        <div class="product-details">
            <div class="product-details-container">
                <div class="product-image-container">
                    <img src="uploads/<?php echo $product['image']; ?>" alt="<?php echo $product['name']; ?>" class="product-image-large">
                </div>
                
                <div class="product-info-container">
                    <h1 class="product-name-large"><?php echo $product['name']; ?></h1>
                    <div class="product-price-large"><?php echo $product['price']; ?> شيقل</div>
                    
                    <div class="product-meta">
                        <div class="product-meta-item">
                            <span class="meta-label">الفئة:</span>
                            <span class="meta-value"><?php echo isset($product['category']) ? $product['category'] : 'غير مصنف'; ?></span>
                        </div>
                        
                        <div class="product-meta-item">
                            <span class="meta-label">الحالة:</span>
                            <span class="meta-value">
                                <?php if (isset($product['quantity']) && $product['quantity'] > 0): ?>
                                    <span style="color: var(--success-color);"><i class="fas fa-check-circle"></i> متوفر</span>
                                <?php else: ?>
                                    <span style="color: var(--danger-color);"><i class="fas fa-times-circle"></i> غير متوفر</span>
                                <?php endif; ?>
                            </span>
                        </div>
                        
                        <div class="product-meta-item">
                            <span class="meta-label">الكمية في الكرتونة:</span>
                            <span class="meta-value"><?php echo isset($product['quantity']) ? $product['quantity'] : 'غير محدد'; ?> <?php echo isset($product['unit']) ? $product['unit'] : 'قطعة'; ?></span>
                        </div>
                    </div>
                    
                    <?php if (isset($product['colors']) && !empty($product['colors'])): ?>
                    <div class="product-colors">
                        <div class="colors-title">الألوان المتاحة:</div>
                        <div class="color-options">
                            <?php foreach ($product['colors'] as $index => $color): ?>
                            <div class="color-option <?php echo $index === 0 ? 'selected' : ''; ?>" 
                                 style="background-color: <?php echo $color; ?>;" 
                                 data-color="<?php echo $color; ?>"
                                 onclick="selectColor(this)"></div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <form class="product-form" method="post" action="product_details.php?id=<?php echo $product['id']; ?>">
                        <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                        <input type="hidden" name="product_name" value="<?php echo $product['name']; ?>">
                        <input type="hidden" name="product_price" value="<?php echo $product['price']; ?>">
                        <input type="hidden" name="product_image" value="<?php echo $product['image']; ?>">
                        <input type="hidden" name="color" id="selected-color" value="<?php echo isset($product['colors'][0]) ? $product['colors'][0] : ''; ?>">
                        
                        <div class="form-group">
                            <label for="quantity" class="form-label">الكمية:</label>
                            <input type="number" id="quantity" name="quantity" class="quantity-input" value="1" min="1" onchange="updateTotalPrice()">
                        </div>
                        
                        <div class="form-group">
                            <label for="unit" class="form-label">الوحدة:</label>
                            <select id="unit" name="unit" class="unit-select" onchange="updateTotalPrice()">
                                <option value="قطعة">قطعة</option>
                                <option value="علبة">علبة</option>
                                <option value="كرتون">كرتون</option>
                                <option value="دزينة">دزينة</option>
                            </select>
                        </div>
                        
                        <div class="total-price-display" id="total-price">
                            الإجمالي: <?php echo $product['price']; ?> شيقل
                        </div>
                        
                        <button type="submit" name="add_to_cart" class="add-to-cart-large">
                            <i class="fas fa-cart-plus"></i> إضافة إلى سلة التسوق
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p><?php echo isset($siteSettings['site']['footer_text']) ? $siteSettings['site']['footer_text'] : 'جميع الحقوق محفوظة - متجر القرطاسية'; ?></p>
    </div>

    <script>
        // تحديث السعر الإجمالي بناءً على الكمية ووحدة القياس
        function updateTotalPrice() {
            const quantityInput = document.getElementById('quantity');
            const unitSelect = document.getElementById('unit');
            const totalPriceDisplay = document.getElementById('total-price');
            
            const quantity = parseInt(quantityInput.value);
            const unit = unitSelect.value;
            const basePrice = <?php echo $product['price']; ?>;
            
            let totalPrice = basePrice * quantity;
            
            // تعديل السعر بناءً على وحدة القياس
            if (unit === 'كرتون') {
                // استخدام كمية الكرتونة من المنتج
                const cartonQuantity = <?php echo isset($product['quantity']) ? $product['quantity'] : 100; ?>;
                totalPrice = basePrice * quantity * cartonQuantity;
            } else if (unit === 'دزينة') {
                // الدزينة تحتوي على 12 قطعة
                totalPrice = basePrice * quantity * 12;
            } else if (unit === 'علبة') {
                // نفترض أن العلبة تحتوي على 10 قطع
                totalPrice = basePrice * quantity * 10;
            }
            
            totalPriceDisplay.textContent = `الإجمالي: ${totalPrice.toFixed(2)} شيقل`;
        }
        
        // تحديد اللون المختار
        function selectColor(element) {
            // إزالة الفئة 'selected' من جميع خيارات الألوان
            const colorOptions = document.querySelectorAll('.color-option');
            colorOptions.forEach(option => {
                option.classList.remove('selected');
            });
            
            // إضافة الفئة 'selected' إلى العنصر المحدد
            element.classList.add('selected');
            
            // تحديث قيمة اللون المختار في النموذج
            document.getElementById('selected-color').value = element.getAttribute('data-color');
        }
        
        // إخفاء الإشعار بعد 3 ثوانٍ
        setTimeout(function() {
            const notification = document.querySelector('.notification');
            if (notification) {
                notification.style.display = 'none';
            }
        }, 3000);
    </script>
</body>
</html>