<?php
session_start();

// التحقق من وجود سلة التسوق في الجلسة
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

// إضافة منتج إلى سلة التسوق
if (isset($_POST['add_to_cart'])) {
    $productId = $_POST['product_id'];
    $productName = $_POST['product_name'];
    $productPrice = $_POST['product_price'];
    $productImage = $_POST['product_image'];
    $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;
    $unit = isset($_POST['unit']) ? $_POST['unit'] : 'قطعة';
    
    // حساب السعر الإجمالي بناءً على الكمية ووحدة القياس
    $totalPrice = calculateTotalPrice($productPrice, $quantity, $unit);
    
    // إضافة المنتج إلى السلة
    $item = [
        'id' => $productId,
        'name' => $productName,
        'price' => $productPrice,
        'image' => $productImage,
        'quantity' => $quantity,
        'unit' => $unit,
        'total_price' => $totalPrice
    ];
    
    $_SESSION['cart'][] = $item;
    
    // إعادة التوجيه لتجنب إعادة إرسال النموذج
    header('Location: customer_products.php?added=1');
    exit;
}

// دالة لحساب السعر الإجمالي بناءً على الكمية ووحدة القياس
function calculateTotalPrice($price, $quantity, $unit) {
    $totalPrice = $price * $quantity;
    
    // تعديل السعر بناءً على وحدة القياس
    if ($unit == 'كرتون') {
        // البحث عن المنتج في قاعدة البيانات للصول على كمية الكرتونة
        $productsData = [];
        $dataFile = 'products_data.json';
        if (file_exists($dataFile)) {
            $productsData = json_decode(file_get_contents($dataFile), true);
        }
        
        // استخدام قيمة الكمية من المنتج إذا كانت موجودة، وإلا استخدام القيمة الافتراضية 100
        $cartonQuantity = 100; // القيمة الافتراضية
        
        // يمكن هنا البحث عن المنتج بالاسم أو المعرف إذا كان متاحاً
        // هذا مجرد مثال بسيط
        foreach ($productsData as $product) {
            if (isset($_POST['product_id']) && isset($product['id']) && $product['id'] == $_POST['product_id']) {
                if (isset($product['quantity'])) {
                    $cartonQuantity = $product['quantity'];
                    break;
                }
            }
        }
        
        $totalPrice = $price * $quantity * $cartonQuantity;
    } elseif ($unit == 'دزينة') {
        // الدزينة تحتوي على 12 قطعة
        $totalPrice = $price * $quantity * 12;
    }
    
    return $totalPrice;
}

// تحميل بيانات المنتجات
$productsData = [];
$dataFile = 'products_data.json';
if (file_exists($dataFile)) {
    $productsData = json_decode(file_get_contents($dataFile), true);
}

// تصنيف المنتجات حسب الفئة
$categories = [];
$categorizedProducts = [];

foreach ($productsData as $index => $product) {
    $category = isset($product['category']) ? $product['category'] : 'أخرى';
    
    if (!in_array($category, $categories)) {
        $categories[] = $category;
        $categorizedProducts[$category] = [];
    }
    
    $product['id'] = $index;
    $categorizedProducts[$category][] = $product;
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منتجاتنا - متجر القرطاسية</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 0;
            margin: 0;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem 0;
            text-align: center;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.2rem;
            margin: 0;
            font-weight: 600;
        }
        
        /* تعديل حجم العنوان للشاشات الصغيرة */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 1.8rem;
            }
        }
        
        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.5rem;
            }
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px 50px;
        }
        
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            margin-bottom: 20px;
        }
        
        /* تعديل شريط التنقل للشاشات الصغيرة */
        @media (max-width: 768px) {
            .navbar {
                flex-direction: column;
                gap: 10px;
            }
        }
        
        .nav-links {
            display: flex;
            gap: 15px;
        }
        
        /* تعديل روابط التنقل للشاشات الصغيرة */
        @media (max-width: 480px) {
            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }
        }
        
        .nav-link {
            text-decoration: none;
            color: var(--dark-color);
            font-weight: 600;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .nav-link:hover {
            color: var(--secondary-color);
        }
        
        .cart-icon {
            position: relative;
        }
        
        .cart-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: var(--accent-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: bold;
        }
        
        .categories {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .category-btn {
            padding: 8px 15px;
            background-color: var(--light-color);
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
        }
        
        /* تعديل أزرار التصنيف للشاشات الصغيرة */
        @media (max-width: 480px) {
            .category-btn {
                padding: 6px 10px;
                font-size: 0.9rem;
            }
        }
        
        .category-btn:hover, .category-btn.active {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .product-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
        }
        
        /* تعديل شبكة المنتجات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .product-gallery {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            }
        }
        
        @media (max-width: 480px) {
            .product-gallery {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 15px;
            }
        }
        
        .product-item {
            background-color: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
        }
        
        .product-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .product-image {
            height: 200px;
            overflow: hidden;
        }
        
        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }
        
        /* تصغير حجم الصور في الشاشات الصغيرة */
        @media (max-width: 768px) {
            .product-image {
                height: 180px;
            }
        }
        
        @media (max-width: 480px) {
            .product-image {
                height: 150px;
            }
        }
        
        @media (max-width: 380px) {
            .product-image {
                height: 130px;
            }
        }
        
        .product-item:hover .product-image img {
            transform: scale(1.05);
        }
        
        .product-info {
            padding: 15px;
        }
        
        .product-name {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--dark-color);
        }
        
        .product-price {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 10px;
        }
        
        .product-form {
            margin-top: 10px;
        }
        
        .quantity-input {
            width: 60px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            margin-left: 5px;
            text-align: center;
        }
        
        .unit-select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            margin-left: 5px;
        }
        
        .add-to-cart {
            width: 100%;
            padding: 10px;
            background-color: var(--secondary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
            margin-top: 10px;
        }
        
        .add-to-cart:hover {
            background-color: #2980b9;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            background-color: var(--success-color);
            color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            z-index: 1000;
            animation: fadeOut 3s forwards;
        }
        
        @keyframes fadeOut {
            0% { opacity: 1; }
            70% { opacity: 1; }
            100% { opacity: 0; visibility: hidden; }
        }
        
        .footer {
            background-color: var(--dark-color);
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 50px;
        }
        
        .total-price-display {
            font-weight: bold;
            margin-top: 10px;
            color: var(--accent-color);
        }
        
        .product-availability {
            margin-bottom: 10px;
            font-size: 0.9rem;
        }
        
        .in-stock {
            color: var(--success-color);
        }
        
        .low-stock {
            color: var(--warning-color);
        }
        
        .out-of-stock {
            color: var(--danger-color);
        }
        
        /* تعديل حجم الخط للأجهزة المحمولة */ 
        @media (max-width: 480px) { 
            .product-name { 
                font-size: 0.9rem; /* تصغير حجم خط اسم المنتج */ 
            } 
            
            .product-price { 
                font-size: 0.95rem; /* تصغير حجم خط سعر المنتج */ 
            } 
            
            .total-price-display { 
                font-size: 0.9rem; /* تصغير حجم خط السعر الإجمالي */ 
                white-space: nowrap; /* منع انتقال النص إلى سطر جديد */
                overflow: hidden; /* إخفاء النص الزائد */
                text-overflow: ellipsis; /* إظهار علامة ... للنص المقطوع */
            } 
        } 
        
        /* تصغير أكثر للشاشات الأصغر */ 
        @media (max-width: 380px) { 
            .product-name { 
                font-size: 0.85rem; 
            } 
            
            .product-price { 
                font-size: 0.9rem; 
            } 
            
            .total-price-display { 
                font-size: 0.85rem; 
                white-space: nowrap; /* منع انتقال النص إلى سطر جديد */
            } 
        }
    </style>
</head>
<body>
    <?php if (isset($_GET['added']) && $_GET['added'] == 1): ?>
    <div class="notification">
        <i class="fas fa-check-circle"></i> تمت إضافة المنتج إلى سلة التسوق بنجاح!
    </div>
    <?php endif; ?>

    <div class="header">
        <h1>منتجاتنا</h1>
    </div>
    
    <div class="container">
        <div class="navbar">
            <div class="nav-links">
                <a href="index.php" class="nav-link"><i class="fas fa-home"></i> الرئيسية</a>
                <a href="customer_products.php" class="nav-link"><i class="fas fa-shopping-bag"></i> المنتجات</a>
            </div>
            <a href="customer_cart.php" class="nav-link cart-icon">
                <i class="fas fa-shopping-cart"></i> سلة التسوق
                <?php if (count($_SESSION['cart']) > 0): ?>
                <span class="cart-count"><?php echo count($_SESSION['cart']); ?></span>
                <?php endif; ?>
            </a>
        </div>
        
        <div class="categories">
            <button class="category-btn active" data-category="all">جميع المنتجات</button>
            <?php foreach ($categories as $category): ?>
            <button class="category-btn" data-category="<?php echo $category; ?>"><?php echo $category; ?></button>
            <?php endforeach; ?>
        </div>
        
        <div class="product-gallery">
            <?php foreach ($categorizedProducts as $category => $products): ?>
                <?php foreach ($products as $product): ?>
                <div class="product-item" data-category="<?php echo $category; ?>">
                    <div class="product-image">
                        <a href="product_details.php?id=<?php echo $product['id']; ?>">
                            <img src="uploads/<?php echo $product['image']; ?>" alt="<?php echo $product['name']; ?>">
                        </a>
                    </div>
                    <div class="product-info">
                        <div class="product-name">
                            <a href="product_details.php?id=<?php echo $product['id']; ?>" style="text-decoration: none; color: inherit;">
                                <?php echo $product['name']; ?>
                            </a>
                        </div>
                        <div class="product-price"><?php echo $product['price']; ?> شيقل</div>
                        
                        <div class="product-availability">
                            <?php if (isset($product['quantity']) && $product['quantity'] > 0): ?>
                                <span class="in-stock">
                                    <i class="fas fa-check-circle"></i> متوفر
                                    <?php if (isset($product['quantity'])): ?>
                                        (<?php echo $product['quantity']; ?> 
                                        <?php echo isset($product['unit']) ? $product['unit'] : 'قطعة'; ?>)
                                    <?php endif; ?>
                                </span>
                            <?php else: ?>
                                <span class="out-of-stock"><i class="fas fa-times-circle"></i> غير متوفر</span>
                            <?php endif; ?>
                        </div>
                        
                        <!-- تعديل عرض حقول الكمية والوحدة -->
                        <form class="product-form" method="post" action="customer_products.php">
                            <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                            <input type="hidden" name="product_name" value="<?php echo $product['name']; ?>">
                            <input type="hidden" name="product_price" value="<?php echo $product['price']; ?>">
                            <input type="hidden" name="product_image" value="<?php echo $product['image']; ?>">
                            <!-- إضافة حقل مخفي لكمية الكرتونة -->
                            <input type="hidden" id="carton-quantity-<?php echo $product['id']; ?>" value="<?php echo isset($product['quantity']) ? $product['quantity'] : 100; ?>">
                            
                            <div class="quantity-unit-container" style="display: flex; align-items: center; gap: 10px;">
                                <div>
                                    <label for="quantity-<?php echo $product['id']; ?>">الكمية:</label>
                                    <input type="number" id="quantity-<?php echo $product['id']; ?>" name="quantity" class="quantity-input" value="1" min="1" onchange="updateTotalPrice(<?php echo $product['id']; ?>)">
                                </div>
                                
                                <div>
                                    <label for="unit-<?php echo $product['id']; ?>">الوحدة:</label>
                                    <select id="unit-<?php echo $product['id']; ?>" name="unit" class="unit-select" onchange="updateTotalPrice(<?php echo $product['id']; ?>)">
                                        <option value="قطعة">قطعة</option>
                                        <option value="علبة">علبة</option>
                                        <option value="كرتون">كرتون</option>
                                        <option value="دزينة">دزينة</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="total-price-display" id="total-price-<?php echo $product['id']; ?>">
                                الإجمالي: <?php echo $product['price']; ?> شيقل
                            </div>
                            
                            <button type="submit" name="add_to_cart" class="add-to-cart">
                                <i class="fas fa-cart-plus"></i> إضافة إلى السلة
                            </button>
                        </form>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endforeach; ?>
        </div>
    </div>
    
    <div class="footer">
        <p>جميع الحقوق محفوظة &copy; 2023 - متجر القرطاسية</p>
    </div>

    <script>
        // تصفية المنتجات حسب الفئة
        document.addEventListener('DOMContentLoaded', function() {
            const categoryButtons = document.querySelectorAll('.category-btn');
            const productItems = document.querySelectorAll('.product-item');
            
            categoryButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const category = this.getAttribute('data-category');
                    
                    // تحديث الزر النشط
                    categoryButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                    
                    // عرض/إخفاء المنتجات
                    productItems.forEach(item => {
                        if (category === 'all' || item.getAttribute('data-category') === category) {
                            item.style.display = 'block';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            });
            
            // إخفاء الإشعار بعد 3 ثوانٍ
            setTimeout(function() {
                const notification = document.querySelector('.notification');
                if (notification) {
                    notification.style.display = 'none';
                }
            }, 3000);
        });
        
        // تحديث السعر الإجمالي بناءً على الكمية ووحدة القياس
        function updateTotalPrice(productId) {
            const quantityInput = document.getElementById('quantity-' + productId);
            const unitSelect = document.getElementById('unit-' + productId);
            const totalPriceDisplay = document.getElementById('total-price-' + productId);
            
            const quantity = parseInt(quantityInput.value);
            const unit = unitSelect.value;
            const basePrice = parseFloat(document.querySelector(`input[name="product_price"][value="${document.querySelector(`#quantity-${productId}`).closest('form').querySelector('input[name="product_price"]').value}"]`).value);
            
            let totalPrice = basePrice * quantity;
            
            // تعديل السعر بناءً على وحدة القياس
            if (unit === 'كرتون') {
                // الحصول على كمية الكرتونة من عنصر مخفي سنضيفه
                let cartonQuantity = 100; // القيمة الافتراضية
                const cartonQuantityInput = document.getElementById('carton-quantity-' + productId);
                if (cartonQuantityInput && cartonQuantityInput.value) {
                    cartonQuantity = parseInt(cartonQuantityInput.value);
                }
                totalPrice = basePrice * quantity * cartonQuantity;
            } else if (unit === 'دزينة') {
                // الدزينة تحتوي على 12 قطعة
                totalPrice = basePrice * quantity * 12;
            }
            
            totalPriceDisplay.textContent = `الإجمالي: ${totalPrice.toFixed(2)} شيقل`;
        }
    </script>
</body>
</html>

