<?php
// استدعاء ملف المصادقة
require_once 'admin_auth.php';

// التحقق من تسجيل الدخول
requireLogin();

// تحميل بيانات المنتجات
$dataFile = 'products_data.json';
$uploadsDir = 'uploads/';

// التحقق من وجود ملف البيانات
if (file_exists($dataFile)) {
    $productsData = json_decode(file_get_contents($dataFile), true);
} else {
    $productsData = [];
}

// تحميل الأقسام من ملف categories_data.json
$categoriesFile = 'categories_data.json';
$availableCategories = [];

if (file_exists($categoriesFile)) {
    $categoriesData = json_decode(file_get_contents($categoriesFile), true);
    
    // استخراج أسماء الأقسام النشطة فقط
    foreach ($categoriesData as $category) {
        if (isset($category['active']) && $category['active']) {
            $availableCategories[] = $category['name'];
        }
    }
} else {
    // قائمة افتراضية في حالة عدم وجود ملف الأقسام
    $availableCategories = [
        'شناتي',
        'مطرات',
        'tornado',
        'Pensan & Fatih',
        'العاب التحدي',
        'مستلزمات تعليمية',
        'قرطاسية',
        'مقالم',
        'صناديق الهدايا',
        'الرسم و الفنون',
        'اكياس الهدايا',
        'أخرى'
    ];
}

// تجميع المنتجات حسب الفئة
$categorizedProducts = [];
$allCategories = [];

// إضافة جميع الأقسام المتاحة أولاً
foreach ($availableCategories as $category) {
    if (!in_array($category, $allCategories)) {
        $allCategories[] = $category;
    }
    
    if (!isset($categorizedProducts[$category])) {
        $categorizedProducts[$category] = [];
    }
}

// ثم إضافة المنتجات إلى الأقسام المناسبة
foreach ($productsData as $index => $product) {
    $category = isset($product['category']) ? $product['category'] : 'أخرى';
    
    if (!in_array($category, $allCategories)) {
        $allCategories[] = $category;
    }
    
    if (!isset($categorizedProducts[$category])) {
        $categorizedProducts[$category] = [];
    }
    
    $categorizedProducts[$category][] = $product;
}

// ترتيب الفئات أبجدياً
sort($allCategories);

// إضافة فئة "الكل" في البداية
array_unshift($allCategories, 'الكل');
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المنتجات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 0;
            margin: 0;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem 0;
            text-align: center;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.2rem;
            margin: 0;
            font-weight: 600;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px 50px;
        }
        
        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
            margin: 1.5rem 0;
            padding: 15px;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }
        
        .action-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 120px;
            height: 120px;
            background-color: white;
            border: 2px solid var(--light-color);
            border-radius: var(--border-radius);
            text-decoration: none;
            color: var(--dark-color);
            transition: var(--transition);
            padding: 10px;
            text-align: center;
        }
        
        .action-button:hover {
            transform: translateY(-5px);
            box-shadow: var(--box-shadow);
            border-color: var(--secondary-color);
        }
        
        .action-button i {
            font-size: 2.5rem;
            margin-bottom: 10px;
            color: var(--secondary-color);
        }
        
        .item-container {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
        }
        
        .tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 20px;
            background-color: white;
            padding: 15px;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow-x: auto;
        }
        
        .tab {
            padding: 10px 20px;
            background-color: var(--light-color);
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            white-space: nowrap;
        }
        
        .tab:hover {
            background-color: #d1d8e0;
        }
        
        .tab.active {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .view-category {
            display: inline-block;
            padding: 8px 15px;
            background-color: var(--success-color);
            color: white;
            border-radius: var(--border-radius);
            text-decoration: none;
            margin-top: 10px;
            transition: var(--transition);
        }
        
        .view-category:hover {
            background-color: #27ae60;
            transform: translateY(-2px);
        }
        
        .product-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .product-item {
            background-color: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
            position: relative;
        }
        
        .product-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .product-image {
            height: 200px;
            overflow: hidden;
            position: relative;
        }
        
        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }
        
        .product-item:hover .product-image img {
            transform: scale(1.05);
        }
        
        .product-info {
            padding: 15px;
        }
        
        .product-name {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 10px;
            color: var(--dark-color);
            height: 40px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
        
        .product-details {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 10px;
            font-size: 0.9rem;
        }
        
        .product-detail {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .product-detail i {
            color: var(--secondary-color);
        }
        
        .product-colors {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
        }
        
        .color-dot {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            border: 1px solid #ddd;
        }
        
        .product-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }
        
        .action-link {
            padding: 5px 10px;
            border-radius: var(--border-radius);
            text-decoration: none;
            color: white;
            font-size: 0.9rem;
            transition: var(--transition);
        }
        
        .edit-link {
            background-color: var(--secondary-color);
        }
        
        .edit-link:hover {
            background-color: #2980b9;
        }
        
        .delete-link {
            background-color: var(--danger-color);
        }
        
        .delete-link:hover {
            background-color: #c0392b;
        }
        
        .empty-category {
            text-align: center;
            padding: 30px;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-top: 20px;
        }
        
        .empty-category i {
            font-size: 3rem;
            color: #d1d8e0;
            margin-bottom: 15px;
        }
        
        .empty-category p {
            color: #7f8c8d;
            font-size: 1.1rem;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            margin-top: 30px;
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .action-buttons {
                justify-content: center;
            }
            
            .action-button {
                width: 100px;
                height: 100px;
            }
            
            .action-button i {
                font-size: 2rem;
            }
            
            .product-gallery {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            }
        }
        
        @media (max-width: 480px) {
            .action-button {
                width: 80px;
                height: 80px;
                font-size: 0.8rem;
            }
            
            .action-button i {
                font-size: 1.5rem;
            }
            
            .product-gallery {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>نظام إدارة المنتجات</h1>
    </div>
    
    <div class="container">
        <!-- أزرار الإجراءات الرئيسية -->
        <div class="action-buttons">
            <a href="add_product.php" class="action-button">
                <i class="fas fa-plus-circle"></i>
                إضافة منتج
            </a>
            <a href="add_multiple.php" class="action-button">
                <i class="fas fa-images"></i>
                إضافة متعددة
            </a>
            <a href="price_offers.php" class="action-button">
                <i class="fas fa-tags"></i>
                عروض الأسعار
            </a>
            <a href="saved_price_offers.php" class="action-button">
                <i class="fas fa-file-invoice-dollar"></i>
                عروض الأسعار المحفوظة
            </a>
            <a href="sales_order.php" class="action-button">
                <i class="fas fa-shopping-cart"></i>
                طلب مبيعات
            </a>
            <a href="orders.php" class="action-button">
                <i class="fas fa-clipboard-list"></i>
                الطلبات
            </a>
            <a href="admin_dashboard.php" class="action-button">
                <i class="fas fa-tachometer-alt"></i>
                لوحة التحكم
            </a>
        </div>
        
        <!-- تبويبات الفئات -->
        <div class="tabs">
            <?php foreach ($allCategories as $index => $category): ?>
                <div class="tab <?php echo ($index === 0) ? 'active' : ''; ?>" data-category="<?php echo $category; ?>">
                    <?php echo $category; ?>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- عرض المنتجات -->
        <?php foreach ($allCategories as $index => $category): ?>
            <div class="category-section" id="category-<?php echo $category; ?>" style="display: <?php echo ($index === 0) ? 'block' : 'none'; ?>">
                <?php if ($category !== 'الكل'): ?>
                    <a href="view_category.php?category=<?php echo urlencode($category); ?>" class="view-category">
                        <i class="fas fa-external-link-alt"></i> عرض قسم <?php echo $category; ?>
                    </a>
                <?php endif; ?>
                
                <div class="product-gallery">
                    <?php 
                    $displayProducts = [];
                    if ($category === 'الكل') {
                        $displayProducts = $productsData;
                    } else {
                        $displayProducts = isset($categorizedProducts[$category]) ? $categorizedProducts[$category] : [];
                    }
                    
                    if (!empty($displayProducts)):
                        foreach ($displayProducts as $index => $product):
                    ?>
                        <div class="product-item">
                            <div class="product-image">
                                <img src="<?php echo $uploadsDir . $product['image']; ?>" alt="<?php echo $product['name']; ?>">
                            </div>
                            <div class="product-info">
                                <div class="product-name"><?php echo $product['name']; ?></div>
                                <div class="product-details" style="display: flex; flex-direction: row; gap: 10px; flex-wrap: wrap;">
                                    <div class="product-detail">
                                        <i class="fas fa-palette"></i>
                                        <?php 
                                        if (isset($product['colors']) && is_array($product['colors'])) {
                                            echo count($product['colors']) . ' لون';
                                        } else {
                                            echo 'لا يوجد';
                                        }
                                        ?>
                                    </div>
                                    <div class="product-detail">
                                        <i class="fas fa-cubes"></i>
                                        <?php echo isset($product['quantity']) ? $product['quantity'] : '0'; ?>
                                        <?php if(isset($product['unit'])): ?>
                                            <i class="fas fa-ruler ml-1"></i> <?php echo $product['unit']; ?>
                                        <?php endif; ?>
                                    </div>
                                    <div class="product-detail">
                                        <i class="fas fa-tag"></i>
                                        <?php echo isset($product['price']) ? $product['price'] . ' ₪' : '0 ₪'; ?>
                                    </div>
                                </div>
                                
                                <?php if (isset($product['colors']) && is_array($product['colors']) && !empty($product['colors'])): ?>
                                <div class="product-colors">
                                    <?php foreach ($product['colors'] as $color): ?>
                                        <div class="color-dot" style="background-color: <?php echo $color; ?>"></div>
                                    <?php endforeach; ?>
                                </div>
                                <?php endif; ?>
                                
                                <div class="product-actions">
                                    <a href="edit.php?id=<?php echo $index; ?>" class="action-link edit-link">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                    <a href="delete.php?id=<?php echo $index; ?>" class="action-link delete-link" onclick="return confirm('هل أنت متأكد من حذف هذا المنتج؟')">
                                        <i class="fas fa-trash"></i> حذف
                                    </a>
                                    <?php if ($product['category'] === 'عروض الأسعار'): ?>
                                    <a href="view_price_offer.php?id=<?php echo $index; ?>" class="action-link edit-link">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php 
                        endforeach;
                    else: 
                    ?>
                        <div class="empty-category">
                            <i class="fas fa-box-open"></i>
                            <p>لا توجد منتجات في هذا القسم</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endforeach; ?>
        
        <div class="footer">
            <p>© 2025 نظام إدارة المنتجات - برمجة وتصميم فراس الكردي</p>
        </div>
    </div>
    
    <script>
        // تبديل بين التبويبات
        document.addEventListener('DOMContentLoaded', function() {
            const tabs = document.querySelectorAll('.tab');
            const categorySections = document.querySelectorAll('.category-section');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // إزالة الفئة النشطة من جميع التبويبات
                    tabs.forEach(t => t.classList.remove('active'));
                    
                    // إضافة الفئة النشطة للتبويب المحدد
                    this.classList.add('active');
                    
                    // إخفاء جميع أقسام الفئات
                    categorySections.forEach(section => {
                        section.style.display = 'none';
                    });
                    
                    // إظهار قسم الفئة المحدد
                    const categoryName = this.getAttribute('data-category');
                    const categorySection = document.getElementById('category-' + categoryName);
                    if (categorySection) {
                        categorySection.style.display = 'block';
                    }
                });
            });
        });
    </script>
</body>
</html>