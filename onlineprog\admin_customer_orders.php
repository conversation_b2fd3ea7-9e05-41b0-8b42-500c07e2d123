<?php
require_once 'admin_auth.php';
require_once 'inventory_management.php';
require_once 'send_email_notification.php';

// التحقق من تسجيل الدخول
requireLogin();

// تحميل بيانات طلبيات العملاء
$ordersFile = 'customer_orders.json';
$customerOrders = [];
if (file_exists($ordersFile) && filesize($ordersFile) > 0) {
    $customerOrders = json_decode(file_get_contents($ordersFile), true);
}

// البحث والتصفية
$searchTerm = isset($_GET['search']) ? $_GET['search'] : '';
$statusFilter = isset($_GET['status']) ? $_GET['status'] : '';
$paymentFilter = isset($_GET['payment_method']) ? $_GET['payment_method'] : '';

// تصفية النتائج
$filteredOrders = [];
foreach ($customerOrders as $order) {
    // تطبيق البحث
    $matchesSearch = empty($searchTerm) || 
                    stripos($order['order_id'], $searchTerm) !== false || 
                    stripos($order['customer_name'], $searchTerm) !== false || 
                    stripos($order['customer_phone'], $searchTerm) !== false;
    
    // تطبيق تصفية الحالة
    $matchesStatus = empty($statusFilter) || $order['status'] == $statusFilter;
    
    // تطبيق تصفية طريقة الدفع
    $matchesPayment = empty($paymentFilter) || $order['payment_method'] == $paymentFilter;
    
    // إضافة الطلب إذا تطابق مع جميع المعايير
    if ($matchesSearch && $matchesStatus && $matchesPayment) {
        $filteredOrders[] = $order;
    }
}

// معالجة تحديث حالة الطلب
if (isset($_POST['update_status']) && isset($_POST['order_id']) && isset($_POST['new_status'])) {
    $orderId = $_POST['order_id'];
    $newStatus = $_POST['new_status'];
    $updated = false;
    
    foreach ($customerOrders as $key => $order) {
        if ($order['order_id'] == $orderId) {
            $oldStatus = $order['status'];
            
            // تحديث حالة الطلب
            $customerOrders[$key]['status'] = $newStatus;
            $updated = true;
            
            // تحديث المخزون إذا لزم الأمر
            updateInventoryOnStatusChange($order, $oldStatus, $newStatus);
            
            // إرسال إشعار بالبريد الإلكتروني للعميل
            if (isset($order['customer_email']) && !empty($order['customer_email'])) {
                $emailSubject = 'تحديث حالة الطلب #' . $orderId;
                $emailBody = createOrderStatusUpdateEmail($order, $newStatus);
                sendEmailNotification($order['customer_email'], $emailSubject, $emailBody);
            }
            
            break;
        }
    }
    
    if ($updated) {
        // حفظ التغييرات
        file_put_contents($ordersFile, json_encode($customerOrders, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        // إعادة التوجيه لتجنب إعادة الإرسال
        header('Location: admin_customer_orders.php?status_updated=1');
        exit;
    }
}

// ترتيب الطلبات حسب التاريخ (الأحدث أولاً)
usort($filteredOrders, function($a, $b) {
    return strtotime($b['order_date']) - strtotime($a['order_date']);
});

// حساب إحصائيات الطلبات
$totalOrders = count($customerOrders);
$pendingOrders = 0;
$processingOrders = 0;
$shippedOrders = 0;
$deliveredOrders = 0;
$cancelledOrders = 0;

foreach ($customerOrders as $order) {
    switch ($order['status']) {
        case 'pending':
            $pendingOrders++;
            break;
        case 'processing':
            $processingOrders++;
            break;
        case 'shipped':
            $shippedOrders++;
            break;
        case 'delivered':
            $deliveredOrders++;
            break;
        case 'cancelled':
            $cancelledOrders++;
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة طلبات العملاء</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2980b9;
            --success-color: #2ecc71;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --info-color: #3498db;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --body-bg: #f8f9fa;
            --card-bg: #ffffff;
            --text-color: #333333;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: var(--body-bg);
            color: var(--text-color);
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }

        .header h1 {
            color: var(--primary-color);
        }

        .back-link {
            color: var(--primary-color);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .back-link:hover {
            color: var(--secondary-color);
        }

        .stats {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .stat-card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 15px;
            box-shadow: var(--box-shadow);
            flex: 1;
            min-width: 150px;
            transition: var(--transition);
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card h3 {
            font-size: 14px;
            margin-bottom: 10px;
            color: #666;
        }

        .stat-card .value {
            font-size: 24px;
            font-weight: bold;
        }

        .filters {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: var(--box-shadow);
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
        }

        .search-box {
            flex: 1;
            min-width: 250px;
        }

        .search-box input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            outline: none;
        }

        .filter-select {
            min-width: 150px;
        }

        .filter-select select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            outline: none;
            background-color: white;
        }

        .filter-btn {
            padding: 10px 20px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
        }

        .filter-btn:hover {
            background-color: var(--secondary-color);
        }

        .orders-table {
            width: 100%;
            border-collapse: collapse;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
            margin-bottom: 20px;
        }

        .orders-table th, .orders-table td {
            padding: 12px 15px;
            text-align: right;
        }

        .orders-table th {
            background-color: var(--primary-color);
            color: white;
        }

        .orders-table tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        .orders-table tr:hover {
            background-color: #e9e9e9;
        }

        .status-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            color: white;
            display: inline-block;
        }

        .status-pending {
            background-color: var(--warning-color);
        }

        .status-processing {
            background-color: var(--info-color);
        }

        .status-shipped {
            background-color: var(--primary-color);
        }

        .status-delivered {
            background-color: var(--success-color);
        }

        .status-cancelled {
            background-color: var(--danger-color);
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            margin-right: 5px;
            font-size: 12px;
        }

        .view-btn {
            background-color: var(--info-color);
            color: white;
        }

        .view-btn:hover {
            background-color: #2980b9;
        }

        .update-btn {
            background-color: var(--success-color);
            color: white;
        }

        .update-btn:hover {
            background-color: #27ae60;
        }

        .delete-btn {
            background-color: var(--danger-color);
            color: white;
        }

        .delete-btn:hover {
            background-color: #c0392b;
        }

        .status-select {
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 5px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination a {
            padding: 8px 12px;
            background-color: var(--light-color);
            color: var(--text-color);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .pagination a:hover, .pagination a.active {
            background-color: var(--primary-color);
            color: white;
        }

        .alert {
            padding: 15px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            color: white;
        }

        .alert-success {
            background-color: var(--success-color);
        }

        .alert-danger {
            background-color: var(--danger-color);
        }

        .no-orders {
            text-align: center;
            padding: 30px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }

        @media (max-width: 768px) {
            .stats {
                flex-direction: column;
            }
            
            .orders-table {
                display: block;
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>إدارة طلبات العملاء</h1>
            <a href="admin_dashboard.php" class="back-link">
                <i class="fas fa-arrow-right"></i>
                العودة للوحة التحكم
            </a>
        </div>

        <?php if (isset($_GET['status_updated'])): ?>
        <div class="alert alert-success">
            تم تحديث حالة الطلب بنجاح
        </div>
        <?php endif; ?>

        <div class="stats">
            <div class="stat-card">
                <h3>إجمالي الطلبات</h3>
                <div class="value"><?php echo $totalOrders; ?></div>
            </div>
            <div class="stat-card">
                <h3>قيد المعالجة</h3>
                <div class="value"><?php echo $pendingOrders; ?></div>
            </div>
            <div class="stat-card">
                <h3>جاري التجهيز</h3>
                <div class="value"><?php echo $processingOrders; ?></div>
            </div>
            <div class="stat-card">
                <h3>تم الشحن</h3>
                <div class="value"><?php echo $shippedOrders; ?></div>
            </div>
            <div class="stat-card">
                <h3>تم التسليم</h3>
                <div class="value"><?php echo $deliveredOrders; ?></div>
            </div>
            <div class="stat-card">
                <h3>ملغية</h3>
                <div class="value"><?php echo $cancelledOrders; ?></div>
            </div>
        </div>

        <form action="" method="get">
            <div class="filters">
                <div class="search-box">
                    <input type="text" name="search" placeholder="بحث برقم الطلب، اسم العميل، أو رقم الهاتف" value="<?php echo htmlspecialchars($searchTerm); ?>">
                </div>
                <div class="filter-select">
                    <select name="status">
                        <option value="">جميع الحالات</option>
                        <option value="pending" <?php echo $statusFilter == 'pending' ? 'selected' : ''; ?>>قيد المعالجة</option>
                        <option value="processing" <?php echo $statusFilter == 'processing' ? 'selected' : ''; ?>>جاري التجهيز</option>
                        <option value="shipped" <?php echo $statusFilter == 'shipped' ? 'selected' : ''; ?>>تم الشحن</option>
                        <option value="delivered" <?php echo $statusFilter == 'delivered' ? 'selected' : ''; ?>>تم التسليم</option>
                        <option value="cancelled" <?php echo $statusFilter == 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                    </select>
                </div>
                <div class="filter-select">
                    <select name="payment_method">
                        <option value="">جميع طرق الدفع</option>
                        <option value="cash" <?php echo $paymentFilter == 'cash' ? 'selected' : ''; ?>>نقدي</option>
                        <option value="card" <?php echo $paymentFilter == 'card' ? 'selected' : ''; ?>>بطاقة ائتمان</option>
                        <option value="bank_transfer" <?php echo $paymentFilter == 'bank_transfer' ? 'selected' : ''; ?>>تحويل بنكي</option>
                    </select>
                </div>
                <button type="submit" class="filter-btn">تصفية</button>
            </div>
        </form>

        <?php if (empty($filteredOrders)): ?>
        <div class="no-orders">
            <i class="fas fa-shopping-cart fa-3x" style="color: #ddd; margin-bottom: 15px;"></i>
            <h2>لا توجد طلبات</h2>
            <p>لم يتم العثور على طلبات تطابق معايير البحث</p>
        </div>
        <?php else: ?>
        <table class="orders-table">
            <thead>
                <tr>
                    <th>رقم الطلب</th>
                    <th>تاريخ الطلب</th>
                    <th>اسم العميل</th>
                    <th>رقم الهاتف</th>
                    <th>المبلغ الإجمالي</th>
                    <th>طريقة الدفع</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($filteredOrders as $order): ?>
                <tr>
                    <td><?php echo htmlspecialchars($order['order_id']); ?></td>
                    <td><?php echo htmlspecialchars($order['order_date']); ?></td>
                    <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                    <td><?php echo htmlspecialchars($order['customer_phone']); ?></td>
                    <td><?php echo number_format($order['total_amount'], 2); ?> شيقل</td>
                    <td>
                        <?php 
                        $paymentMethod = '';
                        switch($order['payment_method']) {
                            case 'cash':
                                $paymentMethod = 'نقدي';
                                break;
                            case 'card':
                                $paymentMethod = 'بطاقة ائتمان';
                                break;
                            case 'bank_transfer':
                                $paymentMethod = 'تحويل بنكي';
                                break;
                            default:
                                $paymentMethod = $order['payment_method'];
                        }
                        echo htmlspecialchars($paymentMethod);
                        ?>
                    </td>
                    <td>
                        <?php 
                        $statusClass = '';
                        $statusText = '';
                        
                        switch($order['status']) {
                            case 'pending':
                                $statusClass = 'status-pending';
                                $statusText = 'قيد المعالجة';
                                break;
                            case 'processing':
                                $statusClass = 'status-processing';
                                $statusText = 'جاري التجهيز';
                                break;
                            case 'shipped':
                                $statusClass = 'status-shipped';
                                $statusText = 'تم الشحن';
                                break;
                            case 'delivered':
                                $statusClass = 'status-delivered';
                                $statusText = 'تم التسليم';
                                break;
                            case 'cancelled':
                                $statusClass = 'status-cancelled';
                                $statusText = 'ملغي';
                                break;
                            default:
                                $statusClass = '';
                                $statusText = $order['status'];
                        }
                        ?>
                        <span class="status-badge <?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                    </td>
                    <td>
                        <a href="admin_customer_order_details.php?id=<?php echo $order['order_id']; ?>" class="action-btn view-btn">
                            <i class="fas fa-eye"></i> عرض
                        </a>
                        
                        <form method="post" action="" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من تحديث حالة الطلب؟')">
                            <input type="hidden" name="order_id" value="<?php echo $order['order_id']; ?>">
                            <select name="new_status" class="status-select">
                                <option value="pending" <?php echo $order['status'] == 'pending' ? 'selected' : ''; ?>>قيد المعالجة</option>
                                <option value="processing" <?php echo $order['status'] == 'processing' ? 'selected' : ''; ?>>جاري التجهيز</option>
                                <option value="shipped" <?php echo $order['status'] == 'shipped' ? 'selected' : ''; ?>>تم الشحن</option>
                                <option value="delivered" <?php echo $order['status'] == 'delivered' ? 'selected' : ''; ?>>تم التسليم</option>
                                <option value="cancelled" <?php echo $order['status'] == 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                            </select>
                            <button type="submit" name="update_status" class="action-btn update-btn">
                                <i class="fas fa-sync-alt"></i> تحديث
                            </button>
                        </form>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php endif; ?>
    </div>

    <script>
        // إخفاء رسالة النجاح بعد 3 ثوانٍ
        document.addEventListener('DOMContentLoaded', function() {
            const alert = document.querySelector('.alert');
            if (alert) {
                setTimeout(function() {
                    alert.style.opacity = '0';
                    alert.style.transition = 'opacity 0.5s';
                    setTimeout(function() {
                        alert.style.display = 'none';
                    }, 500);
                }, 3000);
            }
        });
    </script>
</body>
</html>