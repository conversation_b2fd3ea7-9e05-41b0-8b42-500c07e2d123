<?php
// إنشاء مجلد الرفع إذا لم يكن موجودًا
$uploadsDir = 'uploads/';
if (!file_exists($uploadsDir)) {
    mkdir($uploadsDir, 0777, true);
}

// ملف تخزين بيانات المنتجات
$dataFile = 'products_data.json';

// تحميل البيانات الحالية إذا كانت موجودة
$productsData = [];
if (file_exists($dataFile)) {
    $productsData = json_decode(file_get_contents($dataFile), true);
    if ($productsData === null) {
        $productsData = [];
    }
}

// التحقق من وجود الملفات المرفوعة
if (isset($_FILES['images']) && isset($_POST['names']) && isset($_POST['prices'])) {
    $images = $_FILES['images'];
    $names = $_POST['names'];
    $prices = $_POST['prices'];
    $categories = isset($_POST['categories']) ? $_POST['categories'] : [];
    $colors = isset($_POST['colors']) ? $_POST['colors'] : [];
    $defaultCategory = isset($_POST['default_category']) ? $_POST['default_category'] : 'أخرى';
    $defaultColors = isset($_POST['default_colors']) ? explode(',', $_POST['default_colors']) : ['#000000'];
    
    // معالجة كل صورة
    for ($i = 0; $i < count($images['name']); $i++) {
        if ($images['error'][$i] === 0) {
            $fileName = time() . '_' . $i . '_' . basename($images['name'][$i]);
            $targetPath = $uploadsDir . $fileName;
            
            // رفع الصورة
            if (move_uploaded_file($images['tmp_name'][$i], $targetPath)) {
                // معالجة الألوان المتعددة
                $productColors = [];
                if (isset($colors[$i]) && !empty($colors[$i])) {
                    $productColors = explode(',', $colors[$i]);
                } else {
                    $productColors = $defaultColors;
                }
                
                // تحديد الفئة للمنتج
                $category = $defaultCategory;
                if (isset($categories[$i]) && !empty($categories[$i])) {
                    $category = $categories[$i];
                }
                
                // إضافة بيانات المنتج
                $productsData[] = [
                    'image' => $fileName,
                    'name' => isset($names[$i]) ? $names[$i] : 'صنف ' . ($i + 1),
                    'price' => isset($prices[$i]) ? $prices[$i] : '0',
                    'category' => $category,
                    'colors' => $productColors
                ];
            }
        }
    }
    
    // حفظ البيانات في ملف JSON
    file_put_contents($dataFile, json_encode($productsData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    
    // إضافة تعليمات لمنع التخزين المؤقت
    header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
    header('Cache-Control: post-check=0, pre-check=0', false);
    header('Pragma: no-cache');
    
    // إعادة التوجيه إلى الصفحة الرئيسية مع إضافة طابع زمني
    header('Location: index.php?t=' . time());
    exit;
} else {
    echo 'خطأ: يرجى تحديد الصور وملء جميع الحقول المطلوبة.';
    echo '<br><a href="index.php">العودة إلى الصفحة الرئيسية</a>';
}
?>