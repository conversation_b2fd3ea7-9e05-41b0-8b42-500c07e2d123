<?php
session_start();

// تعريف أدوار المستخدمين
define('ROLE_ADMIN', 'admin'); // مدير كامل الصلاحيات
define('ROLE_MANAGER', 'manager'); // مدير (يمكنه إدارة المنتجات والطلبيات)
define('ROLE_STAFF', 'staff'); // موظف (يمكنه عرض المنتجات والطلبيات فقط)

// بيانات المستخدمين (في الإنتاج يجب تخزينها في قاعدة بيانات)
$users = [
    [
        'id' => 1,
        'username' => 'admin',
        'password' => password_hash('admin123', PASSWORD_DEFAULT),
        'name' => 'مدير النظام',
        'role' => ROLE_ADMIN
    ],
    [
        'id' => 2,
        'username' => 'manager',
        'password' => password_hash('manager123', PASSWORD_DEFAULT),
        'name' => 'مدير المتجر',
        'role' => ROLE_MANAGER
    ],
    [
        'id' => 3,
        'username' => 'staff',
        'password' => password_hash('staff123', PASSWORD_DEFAULT),
        'name' => 'موظف المبيعات',
        'role' => ROLE_STAFF
    ]
];

/**
 * التحقق من تسجيل دخول المستخدم
 * 
 * @return bool هل المستخدم مسجل الدخول
 */
function isLoggedIn() {
    return isset($_SESSION['admin_user_id']);
}

/**
 * التحقق من صلاحيات المستخدم
 * 
 * @param string $requiredRole الدور المطلوب
 * @return bool هل المستخدم يملك الصلاحية المطلوبة
 */
function hasRole($requiredRole) {
    if (!isLoggedIn()) {
        return false;
    }
    
    // التحقق من دور المستخدم
    if ($requiredRole == ROLE_ADMIN) {
        return $_SESSION['admin_user_role'] == ROLE_ADMIN;
    } else if ($requiredRole == ROLE_MANAGER) {
        return $_SESSION['admin_user_role'] == ROLE_ADMIN || $_SESSION['admin_user_role'] == ROLE_MANAGER;
    } else if ($requiredRole == ROLE_STAFF) {
        return true; // جميع المستخدمين لديهم صلاحيات الموظف على الأقل
    }
    
    return false;
}

/**
 * التحقق من تسجيل الدخول وإعادة التوجيه إذا لم يكن مسجلاً
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: admin_login.php');
        exit;
    }
}

/**
 * التحقق من الصلاحيات وإعادة التوجيه إذا لم تكن كافية
 * 
 * @param string $requiredRole الدور المطلوب
 */
function requireRole($requiredRole) {
    requireLogin();
    
    if (!hasRole($requiredRole)) {
        header('Location: admin_access_denied.php');
        exit;
    }
}

/**
 * تسجيل دخول المستخدم
 * 
 * @param string $username اسم المستخدم
 * @param string $password كلمة المرور
 * @return bool نجاح أو فشل تسجيل الدخول
 */
function loginUser($username, $password) {
    global $users;
    
    foreach ($users as $user) {
        if ($user['username'] == $username && password_verify($password, $user['password'])) {
            // تسجيل بيانات المستخدم في الجلسة
            $_SESSION['admin_user_id'] = $user['id'];
            $_SESSION['admin_user_name'] = $user['name'];
            $_SESSION['admin_user_role'] = $user['role'];
            
            return true;
        }
    }
    
    return false;
}

/**
 * تسجيل خروج المستخدم
 */
function logoutUser() {
    // حذف بيانات المستخدم من الجلسة
    unset($_SESSION['admin_user_id']);
    unset($_SESSION['admin_user_name']);
    unset($_SESSION['admin_user_role']);
    
    // إعادة توجيه المستخدم إلى صفحة تسجيل الدخول
    header('Location: admin_login.php');
    exit;
}
?>