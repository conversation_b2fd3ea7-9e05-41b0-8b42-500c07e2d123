<?php
// التحقق من وجود معلمة الفئة
if (!isset($_GET['category'])) {
    header('Location: index.php');
    exit;
}

$category = $_GET['category'];
$dataFile = 'products_data.json';
$uploadsDir = 'uploads/';

// التحقق من وجود ملف البيانات
if (!file_exists($dataFile)) {
    header('Location: index.php');
    exit;
}

// قراءة بيانات المنتجات
$productsData = json_decode(file_get_contents($dataFile), true);

// تجميع المنتجات حسب الفئة المحددة
$categoryProducts = [];
foreach ($productsData as $index => $product) {
    $productCategory = isset($product['category']) ? $product['category'] : 'أخرى';
    if ($productCategory === $category) {
        $categoryProducts[] = $product;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض منتجات <?php echo $category; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        /* أنماط CSS الأساسية */
        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background-color: #2c3e50;
            color: white;
            padding: 15px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 8px;
        }

        .header-buttons {
            margin-top: 15px;
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .back-btn {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            text-decoration: none;
        }

        .back-btn:hover {
            background-color: #2980b9;
        }

        .print-btn {
            display: inline-block;
            background-color: #27ae60;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            text-decoration: none;
            border: none;
            cursor: pointer;
            font-family: 'Tajawal', Arial, sans-serif;
            font-size: 14px;
        }

        .print-btn:hover {
            background-color: #219653;
        }

        @media print {
            .back-btn, .print-btn, .footer {
                display: none !important;
            }

            .header {
                background-color: white !important;
                color: black !important;
                padding: 10px 0 !important;
                margin-bottom: 20px !important;
            }
            
            /* إخفاء عنوان القسم في الهيدر */
            .header h1 {
                display: none !important;
            }

            body {
                background-color: white !important;
            }


            
    /* إخفاء رأس وتذييل الصفحة المطبوعة */
    @page {
        margin: 0.5cm;
        size: auto;
    }
    
    /* إخفاء عنوان URL من رأس وتذييل الصفحة المطبوعة */
    @page :left {
        margin-left: 0;
        margin-right: 0;
    }
    
    @page :right {
        margin-left: 0;
        margin-right: 0;
    }
    
    html {
        margin: 0;
        padding: 0;
    }

            .product-item {
                break-inside: avoid;
                page-break-inside: avoid;
                box-shadow: none !important;
                border: 1px solid #eee !important;
            }

            .gallery {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)) !important;
            }
            
            /* إضافة قواعد للحفاظ على عرض الألوان عند الطباعة */
            .product-colors span, .color-box {
                display: inline-block !important;
                border: 1px solid #000 !important;
                width: 12px !important;
                height: 12px !important;
                border-radius: 2px !important;
                print-color-adjust: exact !important; /* لضمان طباعة الألوان */
                -webkit-print-color-adjust: exact !important; /* لدعم متصفح Safari */
            }
            
            .product-info {
                background-color: white !important;
                border-top: 1px solid #000 !important;
            }
            
            .product-info .name {
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
            }
            
            .product-info .quantity-price {
                display: flex !important;
                justify-content: space-between !important;
            }
            
            .product-info .price {
                color: #000 !important;
            }
            
            /* إخفاء اسم القسم في تفاصيل المنتج */
            .product-category {
                display: none !important;
            }
        }

        .gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .product-item {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .product-item:hover {
            transform: translateY(-5px);
        }

        .product-image {
            height: 200px;
            overflow: hidden;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .product-details {
            padding: 15px;
        }

        .product-category {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 5px;
        }

        .product-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .product-price {
            font-size: 16px;
            color: #e74c3c;
            font-weight: bold;
        }
        
        .product-info {
            display: flex;
            flex-direction: column;
            padding: 10px 15px;
            background-color: #f8f9fa;
            border-top: 1px solid #eee;
        }
        
        .product-info .name {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
            font-weight: bold;
            direction: rtl;
        }
        
        .product-info .product-title {
            text-align: right;
        }
        
        .product-info .product-colors {
            text-align: left;
        }
        
        .product-info .quantity-price {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .product-info .quantity {
            text-align: center;
        }
        
        .product-info .price {
            text-align: center;
            color: #e74c3c;
            font-weight: bold;
        }

        .empty-category {
            text-align: center;
            padding: 30px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding: 15px;
            background-color: #2c3e50;
            color: white;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>عرض منتجات <?php echo $category; ?></h1>
            <div class="header-buttons">
                <a href="index.php" class="back-btn"><i class="fas fa-arrow-right"></i> العودة للصفحة الرئيسية</a>
                <button id="print-btn" class="print-btn"><i class="fas fa-print"></i> طباعة الصفحة</button>
            </div>
        </div>

        <div class="content">
            <?php if (!empty($categoryProducts)): ?>
                <div class="gallery">
                    <?php foreach ($categoryProducts as $product): ?>
                        <div class="product-item">
                            <div class="product-image">
                                <img src="<?php echo $uploadsDir . $product['image']; ?>" alt="<?php echo $product['name']; ?>">
                            </div>
                            <div class="product-info">
                                <div class="name">
                                    <div class="product-title">
                                        <?php echo $product['name']; ?>
                                    </div>
                                    <div class="product-colors">
                                        <?php if(isset($product['colors']) && is_array($product['colors'])): ?>
                                            <div style="display: inline-flex; gap: 3px; vertical-align: middle;">
                                            <?php foreach($product['colors'] as $color): ?>
                                                <span style="display: inline-block; width: 12px; height: 12px; background-color: <?php echo $color; ?>; border: 1px solid #ccc; border-radius: 2px;"></span>
                                            <?php endforeach; ?>
                                            </div>
                                        <?php elseif(isset($product['color'])): ?>
                                            <span class="color-box" style="display: inline-block; width: 15px; height: 15px; background-color: <?php echo $product['color']; ?>; border: 1px solid #ccc; vertical-align: middle;"></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="quantity-price">
                                    <div class="quantity">|  <?php echo isset($product['quantity']) ? $product['quantity'] : '1'; ?> <?php echo isset($product['unit']) ? $product['unit'] : 'قطعة'; ?>  |</div>
                                    <div class="price"><?php echo $product['price']; ?> شيقل</div>
                                </div>
                            </div>
                            <div class="product-details">
                                <div class="product-category"><?php echo isset($product['category']) ? $product['category'] : 'أخرى'; ?></div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="empty-category">لا توجد منتجات في هذا القسم</div>
            <?php endif; ?>
        </div>
    </div>

    <div class="footer">
        <p>جميع الحقوق محفوظة &copy; 2023 - نظام إدارة المنتجات</p>
    </div>

    <script>
        // وظيفة طباعة الصفحة
        document.getElementById('print-btn').addEventListener('click', function() {
            window.print();
        });
    </script>
</body>
</html>