<?php
require_once 'admin_auth.php';

// التحقق من تسجيل الدخول
requireLogin();

// تحميل بيانات الأقسام
$categoriesFile = 'categories_data.json';
$categories = [];

// إنشاء ملف الأقسام إذا لم يكن موجوداً
if (!file_exists($categoriesFile)) {
    // استخراج الأقسام من ملف المنتجات
    $productsData = file_get_contents('products_data.json');
    $products = json_decode($productsData, true);
    
    $uniqueCategories = [];
    $id = 1;
    
    foreach ($products as $product) {
        if (isset($product['category']) && !in_array($product['category'], array_column($uniqueCategories, 'name'))) {
            $uniqueCategories[] = [
                'id' => $id++,
                'name' => $product['category'],
                'image' => 'placeholder.jpg',
                'active' => true
            ];
        }
    }
    
    // حفظ الأقسام في الملف الجديد
    file_put_contents($categoriesFile, json_encode($uniqueCategories, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    $categories = $uniqueCategories;
} else {
    // تحميل الأقسام من الملف
    $categories = json_decode(file_get_contents($categoriesFile), true);
}

// معالجة إضافة قسم جديد
if (isset($_POST['add_category'])) {
    $newCategory = [
        'id' => time(), // استخدام الوقت الحالي كمعرف فريد
        'name' => $_POST['category_name'],
        'image' => 'placeholder.jpg', // صورة افتراضية
        'active' => true
    ];
    
    // معالجة تحميل الصورة إذا تم تقديمها
    if (isset($_FILES['category_image']) && $_FILES['category_image']['error'] == 0) {
        $uploadDir = 'uploads/';
        $fileName = 'category-' . time() . '-' . basename($_FILES['category_image']['name']);
        $uploadFile = $uploadDir . $fileName;
        
        if (move_uploaded_file($_FILES['category_image']['tmp_name'], $uploadFile)) {
            $newCategory['image'] = $fileName;
        }
    }
    
    $categories[] = $newCategory;
    file_put_contents($categoriesFile, json_encode($categories, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    header('Location: admin_categories.php?success=1');
    exit;
}

// معالجة تحديث قسم
if (isset($_POST['update_category'])) {
    $categoryId = $_POST['category_id'];
    
    foreach ($categories as $key => $category) {
        if ($category['id'] == $categoryId) {
            $categories[$key]['name'] = $_POST['category_name'];
            $categories[$key]['active'] = isset($_POST['category_active']) ? true : false;
            
            // معالجة تحميل الصورة إذا تم تقديمها
            if (isset($_FILES['category_image']) && $_FILES['category_image']['error'] == 0) {
                $uploadDir = 'uploads/';
                $fileName = 'category-' . time() . '-' . basename($_FILES['category_image']['name']);
                $uploadFile = $uploadDir . $fileName;
                
                if (move_uploaded_file($_FILES['category_image']['tmp_name'], $uploadFile)) {
                    $categories[$key]['image'] = $fileName;
                }
            }
            
            break;
        }
    }
    
    file_put_contents($categoriesFile, json_encode($categories, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    header('Location: admin_categories.php?updated=1');
    exit;
}

// معالجة حذف قسم
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $categoryId = $_GET['delete'];
    
    foreach ($categories as $key => $category) {
        if ($category['id'] == $categoryId) {
            unset($categories[$key]);
            break;
        }
    }
    
    // إعادة ترتيب المصفوفة
    $categories = array_values($categories);
    
    file_put_contents($categoriesFile, json_encode($categories, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    header('Location: admin_categories.php?deleted=1');
    exit;
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأقسام - نظام إدارة المنتجات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* Header Styles */
        .header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }
        
        .header h1 {
            font-size: 1.5rem;
            margin: 0;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .user-info span {
            margin-left: 10px;
        }
        
        .user-info a {
            color: white;
            text-decoration: none;
            margin-right: 15px;
            opacity: 0.8;
            transition: var(--transition);
        }
        
        .user-info a:hover {
            opacity: 1;
        }
        
        /* Navigation */
        .nav {
            display: flex;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .nav-link {
            padding: 1rem 1.5rem;
            color: var(--dark-color);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            display: flex;
            align-items: center;
        }
        
        .nav-link i {
            margin-left: 8px;
        }
        
        .nav-link.active {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .nav-link:hover:not(.active) {
            background-color: var(--light-color);
        }
        
        /* Main Content */
        .main-content {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .page-title {
            margin-bottom: 1.5rem;
            color: var(--dark-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .btn {
            display: inline-block;
            padding: 0.5rem 1rem;
            background-color: var(--secondary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            text-decoration: none;
            font-size: 0.9rem;
            transition: var(--transition);
        }
        
        .btn:hover {
            background-color: #2980b9;
        }
        
        .btn-success {
            background-color: var(--success-color);
        }
        
        .btn-success:hover {
            background-color: #27ae60;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
        }
        
        .btn-danger:hover {
            background-color: #c0392b;
        }
        
        /* Alert Messages */
        .alert {
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        /* Forms */
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        
        .form-control {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 1rem;
        }
        
        .form-check {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .form-check-input {
            margin-left: 0.5rem;
        }
        
        /* Tables */
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1rem;
        }
        
        .table th,
        .table td {
            padding: 0.75rem;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #555;
        }
        
        .table tr:last-child td {
            border-bottom: none;
        }
        
        .table-actions {
            display: flex;
            gap: 10px;
        }
        
        .category-image-preview {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: var(--border-radius);
            border: 1px solid #ddd;
        }
        
        /* Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            overflow: auto;
            padding: 20px;
        }
        
        .modal-content {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            max-width: 500px;
            margin: 50px auto;
            padding: 2rem;
            position: relative;
        }
        
        .modal-close {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 1.5rem;
            cursor: pointer;
            color: #aaa;
        }
        
        .modal-close:hover {
            color: #333;
        }
        
        /* Footer */
        .footer {
            text-align: center;
            margin-top: 2rem;
            padding: 1rem;
            color: #777;
            font-size: 0.9rem;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .nav {
                flex-direction: column;
            }
            
            .table-responsive {
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>إدارة الأقسام</h1>
            <div class="user-info">
                <span>مرحباً، <?php echo $_SESSION['admin_user_name']; ?></span>
                <a href="admin_login.php?logout=1" title="تسجيل الخروج"><i class="fas fa-sign-out-alt"></i></a>
            </div>
        </header>
        
        <!-- Navigation -->
        <nav class="nav">
            <a href="admin_dashboard.php" class="nav-link"><i class="fas fa-tachometer-alt"></i> الرئيسية</a>
            <a href="admin_categories.php" class="nav-link active"><i class="fas fa-tags"></i> إدارة الأقسام</a>
            <a href="index.php" class="nav-link"><i class="fas fa-box"></i> إدارة المنتجات</a>
            <a href="admin_customer_orders.php" class="nav-link"><i class="fas fa-shopping-cart"></i> طلبيات العملاء</a>
            <a href="customer.php" class="nav-link" target="_blank"><i class="fas fa-globe"></i> زيارة الموقع</a>
        </nav>
        
        <!-- Main Content -->
        <div class="main-content">
            <div class="page-title">
                <h2>إدارة أقسام المنتجات</h2>
                <button class="btn btn-success" id="addCategoryBtn"><i class="fas fa-plus"></i> إضافة قسم جديد</button>
            </div>
            
            <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> تم إضافة القسم بنجاح.
            </div>
            <?php endif; ?>
            
            <?php if (isset($_GET['updated'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> تم تحديث القسم بنجاح.
            </div>
            <?php endif; ?>
            
            <?php if (isset($_GET['deleted'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> تم حذف القسم بنجاح.
            </div>
            <?php endif; ?>
            
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>الصورة</th>
                            <th>اسم القسم</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (count($categories) > 0): ?>
                            <?php foreach ($categories as $index => $category): ?>
                            <tr>
                                <td><?php echo $index + 1; ?></td>
                                <td>
                                    <img src="uploads/<?php echo $category['image']; ?>" alt="<?php echo $category['name']; ?>" class="category-image-preview" onerror="this.src='https://via.placeholder.com/80?text=<?php echo urlencode($category['name']); ?>'">
                                </td>
                                <td><?php echo $category['name']; ?></td>
                                <td>
                                    <?php if ($category['active']): ?>
                                        <span style="color: var(--success-color);"><i class="fas fa-check-circle"></i> نشط</span>
                                    <?php else: ?>
                                        <span style="color: var(--danger-color);"><i class="fas fa-times-circle"></i> غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td class="table-actions">
                                    <button class="btn" onclick="editCategory(<?php echo htmlspecialchars(json_encode($category)); ?>)"><i class="fas fa-edit"></i> تعديل</button>
                                    <a href="admin_categories.php?delete=<?php echo $category['id']; ?>" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا القسم؟')"><i class="fas fa-trash"></i> حذف</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="5" style="text-align: center;">لا توجد أقسام مضافة حتى الآن.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Add Category Modal -->
        <div class="modal" id="addCategoryModal">
            <div class="modal-content">
                <span class="modal-close">&times;</span>
                <h2>إضافة قسم جديد</h2>
                <form action="admin_categories.php" method="post" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="category_name" class="form-label">اسم القسم</label>
                        <input type="text" id="category_name" name="category_name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="category_image" class="form-label">صورة القسم</label>
                        <input type="file" id="category_image" name="category_image" class="form-control" accept="image/*">
                        <small>* اترك هذا الحقل فارغاً لاستخدام صورة افتراضية</small>
                    </div>
                    <button type="submit" name="add_category" class="btn btn-success">إضافة القسم</button>
                </form>
            </div>
        </div>
        
        <!-- Edit Category Modal -->
        <div class="modal" id="editCategoryModal">
            <div class="modal-content">
                <span class="modal-close">&times;</span>
                <h2>تعديل القسم</h2>
                <form action="admin_categories.php" method="post" enctype="multipart/form-data">
                    <input type="hidden" id="edit_category_id" name="category_id">
                    <div class="form-group">
                        <label for="edit_category_name" class="form-label">اسم القسم</label>
                        <input type="text" id="edit_category_name" name="category_name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="edit_category_image" class="form-label">صورة القسم</label>
                        <div id="current_image_preview" style="margin-bottom: 10px;"></div>
                        <input type="file" id="edit_category_image" name="category_image" class="form-control" accept="image/*">
                        <small>* اترك هذا الحقل فارغاً للاحتفاظ بالصورة الحالية</small>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" id="edit_category_active" name="category_active" class="form-check-input">
                        <label for="edit_category_active" class="form-check-label">نشط</label>
                    </div>
                    <button type="submit" name="update_category" class="btn btn-success">حفظ التغييرات</button>
                </form>
            </div>
        </div>
        
        <!-- Footer -->
        <footer class="footer">
            <p>نظام إدارة المنتجات &copy; <?php echo date('Y'); ?> - جميع الحقوق محفوظة</p>
        </footer>
    </div>
    
    <script>
        // إظهار وإخفاء النوافذ المنبثقة
        const addCategoryBtn = document.getElementById('addCategoryBtn');
        const addCategoryModal = document.getElementById('addCategoryModal');
        const editCategoryModal = document.getElementById('editCategoryModal');
        const modalCloseButtons = document.querySelectorAll('.modal-close');
        
        addCategoryBtn.addEventListener('click', function() {
            addCategoryModal.style.display = 'block';
        });
        
        modalCloseButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                addCategoryModal.style.display = 'none';
                editCategoryModal.style.display = 'none';
            });
        });
        
        window.addEventListener('click', function(event) {
            if (event.target === addCategoryModal) {
                addCategoryModal.style.display = 'none';
            }
            if (event.target === editCategoryModal) {
                editCategoryModal.style.display = 'none';
            }
        });
        
        // تعديل القسم
        function editCategory(category) {
            document.getElementById('edit_category_id').value = category.id;
            document.getElementById('edit_category_name').value = category.name;
            document.getElementById('edit_category_active').checked = category.active;
            
            const imagePreview = document.getElementById('current_image_preview');
            imagePreview.innerHTML = `<img src="uploads/${category.image}" alt="${category.name}" class="category-image-preview" onerror="this.src='https://via.placeholder.com/80?text=${encodeURIComponent(category.name)}'">`;
            
            editCategoryModal.style.display = 'block';
        }
    </script>
</body>
</html>