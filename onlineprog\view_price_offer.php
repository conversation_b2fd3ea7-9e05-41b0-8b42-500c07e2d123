<?php
// التحقق من وجود معلمة المعرف
if (!isset($_GET['id'])) {
    header('Location: saved_price_offers.php');
    exit;
}

$offerId = $_GET['id'];
$offersFile = 'price_offers_data.json';

// التحقق من وجود ملف البيانات
if (!file_exists($offersFile)) {
    header('Location: saved_price_offers.php');
    exit;
}

// قراءة بيانات عروض الأسعار
$offersData = json_decode(file_get_contents($offersFile), true);

// البحث عن عرض السعر بواسطة المعرف
$offerFound = false;
$offerDetails = null;

foreach ($offersData as $offer) {
    if ($offer['offer_id'] == $offerId) {
        $offerDetails = $offer;
        $offerFound = true;
        break;
    }
}

// إذا لم يتم العثور على عرض السعر، إعادة التوجيه إلى صفحة عروض الأسعار المحفوظة
if (!$offerFound) {
    header('Location: saved_price_offers.php');
    exit;
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض تفاصيل عرض السعر</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- إضافة مكتبة JsBarcode لتوليد الباركود -->
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 0;
            margin: 0;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem 0;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.2rem;
            margin: 0;
            font-weight: 600;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px 50px;
        }
        
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .card-header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem 1.5rem;
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .card-header i {
            margin-left: 10px;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .btn {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            border: none;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            transition: var(--transition);
            margin-right: 0.5rem;
            font-size: 0.9rem;
        }
        
        .btn-primary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #2980b9;
        }
        
        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        
        .btn-success:hover {
            background-color: #27ae60;
        }
        
        .action-buttons-top {
            display: flex;
            justify-content: flex-start;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .order-info {
            margin-bottom: 2rem;
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: var(--border-radius);
            border-right: 4px solid var(--secondary-color);
        }
        
        .order-info p {
            margin: 0.5rem 0;
        }
        
        .product-list {
            margin-top: 2rem;
        }
        
        .product-item {
            display: flex;
            align-items: flex-start;
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
            position: relative;
            background-color: #fff;
            border-radius: var(--border-radius);
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            margin-bottom: 1rem;
            transition: transform 0.2s ease;
        }
        
        .product-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .product-image {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: var(--border-radius);
            margin-left: 1.5rem;
            border: 1px solid #eee;
        }
        
        .product-details {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .product-info-container {
            display: flex;
            flex-direction: row;
            gap: 1rem;
        }
        
        .barcode-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 0.5rem;
            padding: 0.5rem;
            background-color: white;
            border-radius: var(--border-radius);
            border: 1px solid #eee;
            width: 150px;
        }
        
        .barcode-label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 0.5rem;
        }
        
        .barcode-svg {
            max-width: 100%;
            height: auto;
            background-color: white;
            padding: 0.5rem;
        }
        
        .total-section {
            margin-top: 2rem;
            padding: 1.5rem;
            background-color: #f8f9fa;
            border-radius: var(--border-radius);
            border-right: 4px solid var(--success-color);
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .total-section p {
            margin: 0.8rem 0;
            font-size: 1.1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .total-section .total-profit {
            font-weight: 600;
            color: var(--success-color);
            font-size: 1.3rem;
        }
        
        .print-section {
            margin-top: 2rem;
            text-align: center;
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            text-align: center;
            padding: 1.5rem 0;
            margin-top: 3rem;
        }
        
        @media print {
            .header, .action-buttons-top, .footer, .print-section {
                display: none;
            }
            
            .container {
                width: 100%;
                max-width: 100%;
                padding: 0;
                margin: 0;
            }
            
            .card {
                box-shadow: none;
                border: none;
            }
            
            .card-header {
                background-color: #f8f9fa;
                color: #333;
                border-bottom: 1px solid #ddd;
            }
            
            .product-item {
                break-inside: avoid;
                page-break-inside: avoid;
                border: 1px solid #ddd;
                margin-bottom: 1rem;
            }
            
            .barcode-container {
                break-inside: avoid;
                page-break-inside: avoid;
            }
            
            .barcode-svg {
                max-height: 50px;
            }
            
            .total-section {
                break-inside: avoid;
                page-break-inside: avoid;
                border: 1px solid #ddd;
                margin-top: 2rem;
            }
        }
        
        @media (max-width: 768px) {
            .action-buttons-top {
                flex-direction: column;
            }
            
            .product-item {
                flex-direction: column;
            }
            
            .product-image-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-bottom: 1rem;
            }
            
            .product-image {
                width: 100%;
                max-width: 200px;
                height: auto;
                margin-left: 0;
                margin-bottom: 0.5rem;
            }
            
            .barcode-container {
                width: 100%;
                max-width: 200px;
            }
            
            .product-info {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .info-item {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>تفاصيل عرض السعر</h1>
    </div>
    
    <div class="container">
        <!-- أزرار الإجراءات في أعلى الصفحة -->
        <div class="action-buttons-top">
            <a href="saved_price_offers.php" class="btn btn-primary">
                <i class="fas fa-arrow-right"></i> العودة إلى قائمة عروض الأسعار
            </a>
            <button onclick="window.print()" class="btn btn-success">
                <i class="fas fa-print"></i> طباعة عرض السعر
            </button>
            <a href="export_price_offer_to_excel.php?id=<?php echo $offerId; ?>" class="btn btn-primary">
                <i class="fas fa-file-excel"></i> تصدير إلى إكسل
            </a>
        </div>
        
        <div class="card">
            <div class="card-header">
                <span><i class="fas fa-info-circle"></i> معلومات عرض السعر</span>
            </div>
            <div class="card-body">
                <div class="order-info">
                    <p><strong>رقم العرض:</strong> <?php echo $offerDetails['offer_id']; ?></p>
                    <p><strong>تاريخ العرض:</strong> <?php echo $offerDetails['offer_date']; ?></p>
                    <p><strong>عدد المنتجات:</strong> <?php echo count($offerDetails['products']); ?></p>
                </div>
                
                <!-- عرض قائمة المنتجات -->
                <div class="product-list">
                    <h3>قائمة المنتجات</h3>
                    
                    <?php foreach ($offerDetails['products'] as $index => $product): ?>
                        <?php $totalPrice = $product['sellingPrice'] * $product['quantity']; ?>
                        
                        <div class="product-item">
                            <div class="product-image-container">
                                <?php if (isset($product['image']) && !empty($product['image'])): ?>
                                    <img src="<?php echo $product['image']; ?>" alt="<?php echo $product['name']; ?>" class="product-image">
                                <?php endif; ?>
                                
                                <?php if (isset($product['barcode']) && !empty($product['barcode'])): ?>
                                <div class="barcode-container">
                                    <svg class="barcode-svg" id="barcode-<?php echo $index; ?>"></svg>
                                </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="product-details">
                                <div class="product-name"><?php echo $product['name']; ?></div>
                                <div class="product-info">
                                    <span class="info-item"><i class="fas fa-money-bill-wave"></i> سعر التكلفة: <?php echo $product['costPrice']; ?> شيقل</span>
                                    <span class="info-item"><i class="fas fa-money-bill-wave"></i> سعر البيع: <?php echo $product['sellingPrice']; ?> شيقل</span>
                                    <span class="info-item"><i class="fas fa-sort-amount-up"></i> الكمية: <?php echo $product['quantity']; ?></span>
                                    <span class="info-item"><i class="fas fa-calculator"></i> الإجمالي: <?php echo $totalPrice; ?> شيقل</span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- عرض إجماليات عرض السعر -->
                <div class="total-section">
                    <p><strong>إجمالي التكلفة:</strong> <span><?php echo $offerDetails['total_cost']; ?> شيقل</span></p>
                    <p><strong>إجمالي البيع:</strong> <span><?php echo $offerDetails['total_selling']; ?> شيقل</span></p>
                    <p class="total-profit"><strong>الربح المتوقع:</strong> <span><?php echo $offerDetails['total_profit']; ?> شيقل</span></p>
                </div>
                
                <div class="print-section">
                    <button onclick="window.print()" class="btn btn-success">
                        <i class="fas fa-print"></i> طباعة عرض السعر
                    </button>
                    <a href="export_price_offer_to_excel.php?id=<?php echo $offerId; ?>" class="btn btn-primary">
                        <i class="fas fa-file-excel"></i> تصدير إلى إكسل
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>جميع الحقوق محفوظة &copy; <?php echo date('Y'); ?> نظام إدارة المنتجات</p>
    </div>
    
    <script>
        // توليد الباركود لكل منتج
        document.addEventListener('DOMContentLoaded', function() {
            <?php foreach ($offerDetails['products'] as $index => $product): ?>
                <?php if (isset($product['barcode']) && !empty($product['barcode'])): ?>
                    JsBarcode("#barcode-<?php echo $index; ?>", "<?php echo $product['barcode']; ?>", {
                        format: "EAN13",
                        width: 2,
                        height: 60,
                        displayValue: true,
                        fontSize: 14,
                        margin: 10,
                        background: "#ffffff",
                        lineColor: "#000000",
                        textMargin: 6
                    });
                <?php endif; ?>
            <?php endforeach; ?>
        });
    </script>
</body>
</html>