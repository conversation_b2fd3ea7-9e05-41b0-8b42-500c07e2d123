<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الطلبيات المحفوظة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Se<PERSON>e <PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 0;
            margin: 0;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem 0;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.2rem;
            margin: 0;
            font-weight: 600;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px 50px;
        }
        
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .card-header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem 1.5rem;
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .card-header i {
            margin-left: 10px;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .btn {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            border: none;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            transition: var(--transition);
            margin-right: 0.5rem;
            font-size: 0.9rem;
        }
        
        .btn-primary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #2980b9;
        }
        
        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        
        .btn-success:hover {
            background-color: #27ae60;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #c0392b;
        }
        
        .action-buttons-top {
            display: flex;
            justify-content: flex-start;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .orders-list {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .orders-list th, .orders-list td {
            padding: 0.8rem;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }
        
        .orders-list th {
            background-color: #f2f2f2;
            font-weight: 600;
        }
        
        .orders-list tr:hover {
            background-color: #f5f5f5;
        }
        
        .empty-orders {
            padding: 2rem;
            text-align: center;
            color: #6c757d;
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            text-align: center;
            padding: 1.5rem 0;
            margin-top: 3rem;
        }
        
        @media (max-width: 768px) {
            .action-buttons-top {
                flex-direction: column;
            }
            
            .orders-list th, .orders-list td {
                padding: 0.5rem;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>الطلبيات المحفوظة</h1>
    </div>
    
    <div class="container">
        <!-- أزرار الإجراءات في أعلى الصفحة -->
        <div class="action-buttons-top">
            <a href="index.php" class="btn btn-primary">
                <i class="fas fa-home"></i> الصفحة الرئيسية
            </a>
            <a href="sales_order.php" class="btn btn-success">
                <i class="fas fa-plus-circle"></i> إنشاء طلبية جديدة
            </a>
        </div>
        
        <!-- قسم عرض الطلبيات -->
        <div class="card">
            <div class="card-header">
                <span><i class="fas fa-list"></i> قائمة الطلبيات</span>
            </div>
            <div class="card-body">
                <?php
                // ملف تخزين الطلبيات
                $ordersFile = 'orders_data.json';
                
                // التحقق من وجود ملف الطلبيات
                if (file_exists($ordersFile) && filesize($ordersFile) > 0) {
                    $ordersData = json_decode(file_get_contents($ordersFile), true);
                    
                    // عرض الطلبيات إذا كانت موجودة
                    if (!empty($ordersData)) {
                        echo '<table class="orders-list">';
                        echo '<thead>';
                        echo '<tr>';
                        echo '<th>رقم الطلبية</th>';
                        echo '<th>التاريخ</th>';
                        echo '<th>عدد المنتجات</th>';
                        echo '<th>إجمالي التكلفة</th>';
                        echo '<th>إجمالي البيع</th>';
                        echo '<th>الربح المتوقع</th>';
                        echo '<th>الإجراءات</th>';
                        echo '</tr>';
                        echo '</thead>';
                        echo '<tbody>';
                        
                        // عرض الطلبيات بترتيب تنازلي (الأحدث أولاً)
                        $ordersData = array_reverse($ordersData);
                        
                        foreach ($ordersData as $order) {
                            echo '<tr>';
                            echo '<td>' . $order['order_id'] . '</td>';
                            echo '<td>' . $order['order_date'] . '</td>';
                            echo '<td>' . count($order['products']) . '</td>';
                            echo '<td>' . $order['total_cost'] . ' ريال</td>';
                            echo '<td>' . $order['total_selling'] . ' ريال</td>';
                            echo '<td>' . $order['total_profit'] . ' ريال</td>';
                            echo '<td>';
                            echo '<a href="view_order.php?id=' . $order['order_id'] . '" class="btn btn-primary btn-sm"><i class="fas fa-eye"></i> عرض</a> ';
                            echo '<a href="delete_order.php?id=' . $order['order_id'] . '" class="btn btn-danger btn-sm" onclick="return confirm(\'هل أنت متأكد من حذف هذه الطلبية؟\')">';
                            echo '<i class="fas fa-trash-alt"></i> حذف</a>';
                            echo '</td>';
                            echo '</tr>';
                        }
                        
                        echo '</tbody>';
                        echo '</table>';
                    } else {
                        echo '<div class="empty-orders">لا توجد طلبيات محفوظة</div>';
                    }
                } else {
                    echo '<div class="empty-orders">لا توجد طلبيات محفوظة</div>';
                }
                ?>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>جميع الحقوق محفوظة &copy; <?php echo date('Y'); ?> نظام إدارة المنتجات</p>
    </div>
</body>
</html>