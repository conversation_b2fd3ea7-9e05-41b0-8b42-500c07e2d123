<?php
/**
 * تحديث كمية المنتج في المخزون
 * 
 * @param string $product_id معرف المنتج
 * @param int $quantity_change التغيير في الكمية (سالب للسحب، موجب للإضافة)
 * @return bool نجاح أو فشل التحديث
 */
function updateProductInventory($product_id, $quantity_change) {
    $productsFile = 'products_data.json';
    
    // التحقق من وجود ملف المنتجات
    if (!file_exists($productsFile)) {
        return false;
    }
    
    // تحميل بيانات المنتجات
    $products = json_decode(file_get_contents($productsFile), true);
    $updated = false;
    
    // البحث عن المنتج وتحديث الكمية
    foreach ($products as $key => $product) {
        if ($product['id'] == $product_id) {
            // التحقق من توفر الكمية المطلوبة للسحب
            if ($quantity_change < 0 && abs($quantity_change) > $product['quantity']) {
                return false; // الكمية غير كافية
            }
            
            // تحديث الكمية
            $products[$key]['quantity'] += $quantity_change;
            $updated = true;
            break;
        }
    }
    
    // حفظ التغييرات إذا تم التحديث
    if ($updated) {
        file_put_contents($productsFile, json_encode($products, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        return true;
    }
    
    return false;
}

/**
 * تحديث المخزون عند تغيير حالة الطلب
 * 
 * @param array $order بيانات الطلب
 * @param string $old_status الحالة القديمة
 * @param string $new_status الحالة الجديدة
 * @return bool نجاح أو فشل التحديث
 */
function updateInventoryOnStatusChange($order, $old_status, $new_status) {
    // تحديث المخزون فقط في حالات معينة
    
    // عند تأكيد الطلب (تغيير من قيد المعالجة إلى جاري التجهيز)
    if ($old_status == 'pending' && $new_status == 'processing') {
        // سحب المنتجات من المخزون
        foreach ($order['items'] as $item) {
            if (isset($item['id'])) {
                updateProductInventory($item['id'], -$item['quantity']);
            }
        }
        return true;
    }
    
    // عند إلغاء الطلب بعد تأكيده
    if (($old_status == 'processing' || $old_status == 'shipped') && $new_status == 'cancelled') {
        // إعادة المنتجات إلى المخزون
        foreach ($order['items'] as $item) {
            if (isset($item['id'])) {
                updateProductInventory($item['id'], $item['quantity']);
            }
        }
        return true;
    }
    
    // عند إعادة تنشيط طلب ملغي
    if ($old_status == 'cancelled' && ($new_status == 'processing' || $new_status == 'shipped')) {
        // سحب المنتجات من المخزون مرة أخرى
        foreach ($order['items'] as $item) {
            if (isset($item['id'])) {
                updateProductInventory($item['id'], -$item['quantity']);
            }
        }
        return true;
    }
    
    return false;
}
?>