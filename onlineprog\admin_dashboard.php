<?php
require_once 'admin_auth.php';

// التحقق من تسجيل الدخول
requireLogin();

// تحميل بيانات المنتجات
$productsFile = 'products_data.json';
$products = [];
if (file_exists($productsFile) && filesize($productsFile) > 0) {
    $products = json_decode(file_get_contents($productsFile), true);
}

// تحميل بيانات طلبيات العملاء
$ordersFile = 'customer_orders.json';
$customerOrders = [];
if (file_exists($ordersFile) && filesize($ordersFile) > 0) {
    $customerOrders = json_decode(file_get_contents($ordersFile), true);
}

// حساب الإحصائيات
$totalProducts = count($products);
$totalOrders = count($customerOrders);

// حساب إجمالي المبيعات والأرباح
$totalSales = 0;
$totalProfit = 0;
foreach ($customerOrders as $order) {
    if ($order['status'] != 'cancelled') {
        $totalSales += $order['total_amount'];
        
        // حساب الربح (نفترض أن الربح هو 20% من سعر البيع)
        $profit = $order['total_amount'] * 0.2;
        $totalProfit += $profit;
    }
}

// حساب عدد الطلبات حسب الحالة
$orderStatusCounts = [
    'pending' => 0,
    'processing' => 0,
    'shipped' => 0,
    'delivered' => 0,
    'cancelled' => 0
];

foreach ($customerOrders as $order) {
    if (isset($orderStatusCounts[$order['status']])) {
        $orderStatusCounts[$order['status']]++;
    }
}

// حساب المبيعات الشهرية للسنة الحالية
$currentYear = date('Y');
$monthlySales = array_fill(0, 12, 0);

foreach ($customerOrders as $order) {
    if ($order['status'] != 'cancelled') {
        $orderDate = strtotime($order['order_date']);
        $orderYear = date('Y', $orderDate);
        $orderMonth = date('n', $orderDate) - 1; // 0-11 للمصفوفة
        
        if ($orderYear == $currentYear) {
            $monthlySales[$orderMonth] += $order['total_amount'];
        }
    }
}

// حساب عدد المنتجات حسب الفئة
$categoryCounts = [];
foreach ($products as $product) {
    $category = $product['category'];
    if (!isset($categoryCounts[$category])) {
        $categoryCounts[$category] = 0;
    }
    $categoryCounts[$category]++;
}

// تحديد المنتجات منخفضة المخزون (أقل من 10 قطع)
$lowStockProducts = [];
foreach ($products as $product) {
    if ($product['quantity'] < 10) {
        $lowStockProducts[] = $product;
    }
}

// ترتيب المنتجات منخفضة المخزون تصاعدياً حسب الكمية
usort($lowStockProducts, function($a, $b) {
    return $a['quantity'] - $b['quantity'];
});

// تحديد أكثر 5 منتجات منخفضة المخزون
$lowStockProducts = array_slice($lowStockProducts, 0, 5);
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة المنتجات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* Header Styles */
        .header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }
        
        .header h1 {
            font-size: 1.5rem;
            margin: 0;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .user-info span {
            margin-left: 10px;
        }
        
        .user-info a {
            color: white;
            text-decoration: none;
            margin-right: 15px;
            opacity: 0.8;
            transition: var(--transition);
        }
        
        .user-info a:hover {
            opacity: 1;
        }
        
        /* Navigation */
        .nav {
            display: flex;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .nav-link {
            padding: 1rem 1.5rem;
            color: var(--dark-color);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            display: flex;
            align-items: center;
        }
        
        .nav-link i {
            margin-left: 8px;
        }
        
        .nav-link.active {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .nav-link:hover:not(.active) {
            background-color: var(--light-color);
        }
        
        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            transition: var(--transition);
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }
        
        .stat-card .icon {
            font-size: 2rem;
            margin-bottom: 1rem;
            align-self: flex-start;
        }
        
        .stat-card .title {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 0.5rem;
        }
        
        .stat-card .value {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-card .trend {
            font-size: 0.85rem;
            display: flex;
            align-items: center;
        }
        
        .stat-card .trend i {
            margin-left: 5px;
        }
        
        .stat-card.products .icon {
            color: var(--secondary-color);
        }
        
        .stat-card.orders .icon {
            color: var(--warning-color);
        }
        
        .stat-card.sales .icon {
            color: var(--success-color);
        }
        
        .stat-card.profit .icon {
            color: var(--accent-color);
        }
        
        /* Charts Section */
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 2rem;
        }
        
        .chart-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 1.5rem;
            transition: var(--transition);
        }
        
        .chart-card h3 {
            margin-bottom: 1rem;
            color: var(--dark-color);
            font-size: 1.2rem;
        }
        
        .chart-container {
            position: relative;
            height: 250px;
        }
        
        /* Low Stock Products */
        .low-stock-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .low-stock-card h3 {
            margin-bottom: 1rem;
            color: var(--dark-color);
            font-size: 1.2rem;
        }
        
        .low-stock-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .low-stock-table th,
        .low-stock-table td {
            padding: 0.75rem;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        
        .low-stock-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #555;
        }
        
        .low-stock-table tr:last-child td {
            border-bottom: none;
        }
        
        .low-stock-table .quantity {
            font-weight: bold;
            color: var(--danger-color);
        }
        
        .low-stock-table .edit-link {
            color: var(--secondary-color);
            text-decoration: none;
        }
        
        .low-stock-table .edit-link:hover {
            text-decoration: underline;
        }
        
        .empty-message {
            text-align: center;
            padding: 2rem;
            color: #777;
            font-style: italic;
        }
        
        /* Footer */
        .footer {
            text-align: center;
            margin-top: 2rem;
            padding: 1rem;
            color: #777;
            font-size: 0.9rem;
        }
        
        /* Responsive */
        @media (max-width: 992px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .charts-grid {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 576px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .header {
                flex-direction: column;
                text-align: center;
            }
            
            .user-info {
                margin-top: 1rem;
            }
            
            .nav {
                flex-direction: column;
            }
            
            .chart-container {
                height: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>لوحة التحكم</h1>
            <div class="user-info">
                <span>مرحباً، <?php echo $_SESSION['admin_user_name']; ?></span>
                <a href="admin_login.php?logout=1" title="تسجيل الخروج"><i class="fas fa-sign-out-alt"></i></a>
            </div>
        </header>
        
        <!-- Navigation -->
        <nav class="nav">
            <a href="admin_dashboard.php" class="nav-link active"><i class="fas fa-tachometer-alt"></i> الرئيسية</a>
            <a href="admin_categories.php" class="nav-link"><i class="fas fa-tags"></i> إدارة الأقسام</a>
            <a href="orders.php" class="nav-link"><i class="fas fa-box"></i> طلبيات المخزون</a>
            <a href="admin_customer_orders.php" class="nav-link"><i class="fas fa-shopping-cart"></i> طلبيات العملاء</a>
            <a href="admin_settings.php" class="nav-link"><i class="fas fa-cog"></i> إعدادات الموقع</a>
            <a href="index.php" class="nav-link" target="_blank"><i class="fas fa-globe"></i> زيارة الموقع</a>
        </nav>
        
        <!-- Stats Cards -->
        <div class="stats-grid">
            <div class="stat-card products">
                <div class="icon"><i class="fas fa-cubes"></i></div>
                <div class="title">إجمالي المنتجات</div>
                <div class="value"><?php echo $totalProducts; ?></div>
                <div class="trend">
                    <i class="fas fa-box"></i> منتج في المخزون
                </div>
            </div>
            
            <div class="stat-card orders">
                <div class="icon"><i class="fas fa-shopping-cart"></i></div>
                <div class="title">طلبيات العملاء</div>
                <div class="value"><?php echo $totalOrders; ?></div>
                <div class="trend">
                    <i class="fas fa-clock"></i> <?php echo $orderStatusCounts['pending']; ?> طلب قيد الانتظار
                </div>
            </div>
            
            <div class="stat-card sales">
                <div class="icon"><i class="fas fa-money-bill-wave"></i></div>
                <div class="title">إجمالي المبيعات</div>
                <div class="value"><?php echo number_format($totalSales, 2); ?> ₪</div>
                <div class="trend">
                    <i class="fas fa-chart-line"></i> إجمالي المبيعات
                </div>
            </div>
            
            <div class="stat-card profit">
                <div class="icon"><i class="fas fa-chart-pie"></i></div>
                <div class="title">إجمالي الأرباح</div>
                <div class="value"><?php echo number_format($totalProfit, 2); ?> ₪</div>
                <div class="trend">
                    <i class="fas fa-percentage"></i> تقديري (20% من المبيعات)
                </div>
            </div>
        </div>
        
        <!-- Charts -->
        <div class="charts-grid">
            <div class="chart-card">
                <h3>حالة الطلبيات</h3>
                <div class="chart-container">
                    <canvas id="orderStatusChart"></canvas>
                </div>
            </div>
            
            <div class="chart-card">
                <h3>المبيعات الشهرية (<?php echo $currentYear; ?>)</h3>
                <div class="chart-container">
                    <canvas id="monthlySalesChart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="charts-grid">
            <div class="chart-card">
                <h3>توزيع المنتجات حسب الفئة</h3>
                <div class="chart-container">
                    <canvas id="categoriesChart"></canvas>
                </div>
            </div>
            
            <div class="low-stock-card">
                <h3>منتجات منخفضة المخزون</h3>
                <?php if (count($lowStockProducts) > 0): ?>
                <table class="low-stock-table">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>الكمية المتبقية</th>
                            <th>السعر</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($lowStockProducts as $product): ?>
                        <tr>
                            <td><?php echo $product['name']; ?></td>
                            <td class="quantity"><?php echo $product['quantity']; ?></td>
                            <td><?php echo number_format($product['price'], 2); ?> ₪</td>
                            <td>
                                <a href="edit.php?id=<?php echo $product['id']; ?>" class="edit-link">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <?php else: ?>
                <div class="empty-message">
                    <i class="fas fa-check-circle"></i> لا توجد منتجات منخفضة المخزون حالياً
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Footer -->
        <footer class="footer">
            <p>نظام إدارة المنتجات &copy; <?php echo date('Y'); ?> - جميع الحقوق محفوظة</p>
        </footer>
    </div>
    
    <script>
        // رسم بياني لحالة الطلبيات
        const orderStatusCtx = document.getElementById('orderStatusChart').getContext('2d');
        const orderStatusChart = new Chart(orderStatusCtx, {
            type: 'pie',
            data: {
                labels: ['قيد الانتظار', 'قيد المعالجة', 'تم الشحن', 'تم التسليم', 'ملغي'],
                datasets: [{
                    data: [
                        <?php echo $orderStatusCounts['pending']; ?>,
                        <?php echo $orderStatusCounts['processing']; ?>,
                        <?php echo $orderStatusCounts['shipped']; ?>,
                        <?php echo $orderStatusCounts['delivered']; ?>,
                        <?php echo $orderStatusCounts['cancelled']; ?>
                    ],
                    backgroundColor: [
                        '#f39c12', // قيد الانتظار
                        '#3498db', // قيد المعالجة
                        '#2ecc71', // تم الشحن
                        '#27ae60', // تم التسليم
                        '#e74c3c'  // ملغي
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
        
        // رسم بياني للمبيعات الشهرية
        const monthlySalesCtx = document.getElementById('monthlySalesChart').getContext('2d');
        const monthlySalesChart = new Chart(monthlySalesCtx, {
            type: 'bar',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                datasets: [{
                    label: 'المبيعات (₪)',
                    data: [
                        <?php echo implode(',', $monthlySales); ?>
                    ],
                    backgroundColor: '#3498db',
                    borderColor: '#2980b9',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // رسم بياني لتوزيع المنتجات حسب الفئة
        const categoriesCtx = document.getElementById('categoriesChart').getContext('2d');
        const categoriesChart = new Chart(categoriesCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    <?php 
                    $categoryLabels = [];
                    $categoryData = [];
                    foreach ($categoryCounts as $category => $count) {
                        $categoryLabels[] = "'$category'";
                        $categoryData[] = $count;
                    }
                    echo implode(',', $categoryLabels);
                    ?>
                ],
                datasets: [{
                    data: [
                        <?php echo implode(',', $categoryData); ?>
                    ],
                    backgroundColor: [
                        '#1abc9c', '#2ecc71', '#3498db', '#9b59b6', '#f1c40f', 
                        '#e67e22', '#e74c3c', '#34495e', '#16a085', '#27ae60', 
                        '#2980b9', '#8e44ad', '#f39c12', '#d35400', '#c0392b'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    </script>
</body>
</html>