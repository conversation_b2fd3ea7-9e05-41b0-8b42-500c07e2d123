<?php
// إنشاء مجلد الرفع إذا لم يكن موجودًا
$uploadsDir = 'uploads/';
if (!file_exists($uploadsDir)) {
    mkdir($uploadsDir, 0777, true);
}

// ملف تخزين بيانات المنتجات
$dataFile = 'products_data.json';

// تحميل البيانات الحالية إذا كانت موجودة
$productsData = [];
if (file_exists($dataFile)) {
    $productsData = json_decode(file_get_contents($dataFile), true);
}

// التحقق من وجود الملفات المرفوعة
if (isset($_FILES['csv_file']) && isset($_FILES['images_zip'])) {
    $csvFile = $_FILES['csv_file'];
    $imagesZip = $_FILES['images_zip'];
    
    // التحقق من نوع الملفات
    $csvMimeType = mime_content_type($csvFile['tmp_name']);
    $zipMimeType = mime_content_type($imagesZip['tmp_name']);
    
    if (($csvMimeType === 'text/csv' || $csvMimeType === 'text/plain') && 
        ($zipMimeType === 'application/zip' || $zipMimeType === 'application/x-zip-compressed')) {
        
        // إنشاء مجلد مؤقت لاستخراج ملفات ZIP
        $tempDir = 'temp_' . time() . '/';
        mkdir($uploadsDir . $tempDir, 0777, true);
        
        // استخراج ملف ZIP
        $zip = new ZipArchive;
        if ($zip->open($imagesZip['tmp_name']) === TRUE) {
            $zip->extractTo($uploadsDir . $tempDir);
            $zip->close();
            
            // قراءة ملف CSV
            if (($handle = fopen($csvFile['tmp_name'], 'r')) !== FALSE) {
                // قراءة الصف الأول (العناوين)
                $headers = fgetcsv($handle, 1000, ',');
                
                // التحقق من وجود الأعمدة المطلوبة
                $nameIndex = array_search('اسم_الصنف', $headers);
                $priceIndex = array_search('السعر', $headers);
                $imageIndex = array_search('اسم_الصورة', $headers);
                
                if ($nameIndex !== false && $priceIndex !== false && $imageIndex !== false) {
                    // قراءة بقية الصفوف
                    while (($data = fgetcsv($handle, 1000, ',')) !== FALSE) {
                        $imageName = $data[$imageIndex];
                        $productName = $data[$nameIndex];
                        $productPrice = $data[$priceIndex];
                        
                        // البحث عن الصورة في المجلد المؤقت
                        $foundImage = false;
                        $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($uploadsDir . $tempDir));
                        
                        foreach ($iterator as $file) {
                            if ($file->isFile() && basename($file->getPathname()) === $imageName) {
                                $newFileName = time() . '_' . basename($file->getPathname());
                                copy($file->getPathname(), $uploadsDir . $newFileName);
                                
                                // إضافة بيانات المنتج
                                $productsData[] = [
                                    'image' => $newFileName,
                                    'name' => $productName,
                                    'price' => $productPrice
                                ];
                                
                                $foundImage = true;
                                break;
                            }
                        }
                    }
                    
                    // حفظ البيانات في ملف JSON
                    file_put_contents($dataFile, json_encode($productsData, JSON_PRETTY_PRINT));
                }
                
                fclose($handle);
            }
            
            // حذف المجلد المؤقت
            deleteDirectory($uploadsDir . $tempDir);
        }
    }
    
    // إعادة التوجيه إلى الصفحة الرئيسية
    header('Location: index.php');
    exit;
} else {
    echo 'خطأ: يرجى تحديد ملف CSV وملف ZIP.';
    echo '<br><a href="index.php">العودة إلى الصفحة الرئيسية</a>';
}

// دالة لحذف مجلد وجميع محتوياته
function deleteDirectory($dir) {
    if (!file_exists($dir)) {
        return true;
    }
    
    if (!is_dir($dir)) {
        return unlink($dir);
    }
    
    foreach (scandir($dir) as $item) {
        if ($item == '.' || $item == '..') {
            continue;
        }
        
        if (!deleteDirectory($dir . DIRECTORY_SEPARATOR . $item)) {
            return false;
        }
    }
    
    return rmdir($dir);
}
?>