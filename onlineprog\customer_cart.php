<?php
session_start();

// التحقق من وجود سلة التسوق في الجلسة
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

// تحديث الكمية
if (isset($_POST['update_quantity'])) {
    $cartIndex = $_POST['cart_index'];
    $newQuantity = intval($_POST['quantity']);
    
    if (isset($_SESSION['cart'][$cartIndex]) && $newQuantity > 0) {
        $_SESSION['cart'][$cartIndex]['quantity'] = $newQuantity;
    }
    
    // إعادة التوجيه لتجنب إعادة إرسال النموذج
    header('Location: customer_cart.php');
    exit;
}

// حذف منتج من السلة
if (isset($_POST['remove_item'])) {
    $cartIndex = $_POST['cart_index'];
    
    if (isset($_SESSION['cart'][$cartIndex])) {
        unset($_SESSION['cart'][$cartIndex]);
        // إعادة ترتيب المصفوفة
        $_SESSION['cart'] = array_values($_SESSION['cart']);
    }
    
    // إعادة التوجيه لتجنب إعادة إرسال النموذج
    header('Location: customer_cart.php');
    exit;
}

// حساب المجموع الكلي
$totalAmount = 0;
foreach ($_SESSION['cart'] as $item) {
    $totalAmount += $item['price'] * $item['quantity'];
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سلة التسوق</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 0;
            margin: 0;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem 0;
            text-align: center;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.2rem;
            margin: 0;
            font-weight: 600;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px 50px;
        }
        
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            margin-bottom: 20px;
        }
        
        .nav-links {
            display: flex;
            gap: 15px;
        }
        
        .nav-link {
            text-decoration: none;
            color: var(--dark-color);
            font-weight: 600;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .nav-link:hover {
            color: var(--secondary-color);
        }
        
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .card-header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem 1.5rem;
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .card-header i {
            margin-left: 10px;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .empty-cart {
            text-align: center;
            padding: 2rem;
        }
        
        .empty-cart i {
            font-size: 4rem;
            color: var(--light-color);
            margin-bottom: 1rem;
        }
        
        .empty-cart p {
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
        }
        
        .cart-items {
            margin-bottom: 2rem;
        }
        
        .cart-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--light-color);
            position: relative;
        }
        
        .cart-item:last-child {
            border-bottom: none;
        }
        
        .item-image {
            width: 80px;
            height: 80px;
            border-radius: var(--border-radius);
            overflow: hidden;
            margin-left: 1rem;
        }
        
        .item-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .item-details {
            flex: 1;
        }
        
        .item-name {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }
        
        .item-price {
            color: var(--secondary-color);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .item-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .quantity-control {
            display: flex;
            align-items: center;
        }
        
        .quantity-control input {
            width: 60px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            text-align: center;
            margin: 0 10px;
        }
        
        .update-btn {
            background-color: var(--secondary-color);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
        }
        
        .update-btn:hover {
            background-color: #2980b9;
        }
        
        .remove-btn {
            background-color: var(--danger-color);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
        }
        
        .remove-btn:hover {
            background-color: #c0392b;
        }
        
        .cart-summary {
            background-color: #f8f9fa;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border-right: 4px solid var(--secondary-color);
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .summary-row.total {
            font-weight: 700;
            font-size: 1.3rem;
            color: var(--dark-color);
            border-top: 1px solid #ddd;
            padding-top: 1rem;
        }
        
        .checkout-btn {
            display: block;
            width: 100%;
            background-color: var(--success-color);
            color: white;
            border: none;
            padding: 12px;
            border-radius: var(--border-radius);
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            text-align: center;
            text-decoration: none;
            margin-top: 1.5rem;
        }
        
        .checkout-btn:hover {
            background-color: #27ae60;
        }
        
        .continue-shopping {
            display: inline-block;
            margin-top: 1rem;
            color: var(--secondary-color);
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
        }
        
        .continue-shopping:hover {
            color: var(--primary-color);
        }
        
        .continue-shopping i {
            margin-left: 5px;
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            text-align: center;
            padding: 1.5rem 0;
            margin-top: 3rem;
        }
        
        @media (max-width: 768px) {
            .cart-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .item-image {
                margin-bottom: 1rem;
                margin-left: 0;
            }
            
            .item-actions {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
                margin-top: 1rem;
            }
            
            .navbar {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>سلة التسوق</h1>
    </div>
    
    <div class="container">
        <div class="navbar">
            <div class="nav-links">
                <a href="customer_products.php" class="nav-link"><i class="fas fa-home"></i> الرئيسية</a>
                <?php if (isset($_SESSION['customer_id'])): ?>
                    <a href="customer_orders.php" class="nav-link"><i class="fas fa-box"></i> طلبياتي</a>
                    <a href="customer_logout.php" class="nav-link"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                <?php else: ?>
                    <a href="customer_login.php" class="nav-link"><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</a>
                    <a href="customer_register.php" class="nav-link"><i class="fas fa-user-plus"></i> إنشاء حساب</a>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <span><i class="fas fa-shopping-cart"></i> سلة التسوق</span>
            </div>
            <div class="card-body">
                <?php if (empty($_SESSION['cart'])): ?>
                    <div class="empty-cart">
                        <i class="fas fa-shopping-cart"></i>
                        <p>سلة التسوق فارغة</p>
                        <a href="customer_products.php" class="continue-shopping"><i class="fas fa-arrow-right"></i> العودة للتسوق</a>
                    </div>
                <?php else: ?>
                    <div class="cart-items">
                        <?php foreach ($_SESSION['cart'] as $index => $item): ?>
                            <div class="cart-item">
                                <div class="item-image">
                                    <?php if (!empty($item['image'])): ?>
                                        <img src="<?php echo $item['image']; ?>" alt="<?php echo $item['name']; ?>">
                                    <?php else: ?>
                                        <img src="https://via.placeholder.com/80" alt="صورة المنتج">
                                    <?php endif; ?>
                                </div>
                                
                                <div class="item-details">
                                    <div class="item-name"><?php echo $item['name']; ?></div>
                                    <div class="item-price"><?php echo $item['price']; ?> شيقل</div>
                                    <div class="item-actions">
                                        <form method="post" class="quantity-control">
                                            <input type="hidden" name="cart_index" value="<?php echo $index; ?>">
                                            <input type="number" name="quantity" value="<?php echo $item['quantity']; ?>" min="1">
                                            <button type="submit" name="update_quantity" class="update-btn">تحديث</button>
                                        </form>
                                        
                                        <form method="post">
                                            <input type="hidden" name="cart_index" value="<?php echo $index; ?>">
                                            <button type="submit" name="remove_item" class="remove-btn">حذف</button>
                                        </form>
                                    </div>
                                </div>
                                
                                <div class="item-total">
                                    <strong>المجموع: <?php echo $item['price'] * $item['quantity']; ?> شيقل</strong>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="cart-summary">
                        <div class="summary-row total">
                            <span>المجموع الكلي:</span>
                            <span><?php echo $totalAmount; ?> شيقل</span>
                        </div>
                        
                        <?php if (isset($_SESSION['customer_id'])): ?>
                            <a href="customer_checkout.php" class="checkout-btn">إتمام الطلب</a>
                        <?php else: ?>
                            <a href="customer_login.php?redirect=checkout" class="checkout-btn">تسجيل الدخول لإتمام الطلب</a>
                        <?php endif; ?>
                        
                        <a href="customer_products.php" class="continue-shopping"><i class="fas fa-arrow-right"></i> متابعة التسوق</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>جميع الحقوق محفوظة &copy; <?php echo date('Y'); ?> متجر المنتجات</p>
    </div>
    
    
  <!-- 🚩 Bottom Navigation Bar Start -->
<style>
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70px;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -1px 10px rgba(0,0,0,0.1);
  padding: 0 10px;
  z-index: 999;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #aaa;
  font-size: 12px;
  text-decoration: none;
}

.nav-item .icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.nav-item.active {
  color: #3b5bdb;
}

.nav-center-btn {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(0,0,0,0.15);
  display: flex;
  align-items: center;
  justify-content: center;
}

.center-button {
  background-color: #6c4ce3;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-size: 24px;
}
</style>

<div class="bottom-nav">
  <a href="customer.php" class="nav-item active">
    <i class="icon">🏠</i>
    <span>Home</span>
  </a>
  <a href="#" class="nav-item">
    <i class="icon">🔍</i>
    <span>Search</span>
  </a>

  <div class="nav-center-btn">
    <a href="#" class="center-button">
      <i class="icon">🏬</i>
    </a>
  </div>

  <a href="customer_cart.php" class="nav-item">
    <i class="icon">🛒</i>
    <span>Cart</span>
  </a>
  <a href="#" class="nav-item">
    <i class="icon">👤</i>
    <span>Profile</span>
  </a>
</div>
<!-- 🚩 Bottom Navigation Bar End -->

</body>
</html>
   
    
    
    
</body>
</html>