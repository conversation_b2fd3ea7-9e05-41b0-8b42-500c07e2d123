<?php
// ملف تخزين بيانات المنتجات
$dataFile = 'products_data.json';
$uploadsDir = 'uploads/';

// التحقق من وجود معرف المنتج
if (!isset($_GET['id'])) {
    header('Location: index.php');
    exit;
}

$id = (int)$_GET['id'];

// تحميل البيانات الحالية
if (file_exists($dataFile)) {
    $productsData = json_decode(file_get_contents($dataFile), true);
    
    // التحقق من وجود المنتج
    if (!isset($productsData[$id])) {
        header('Location: index.php');
        exit;
    }
    
    $product = $productsData[$id];
} else {
    header('Location: index.php');
    exit;
}

// رسالة نجاح أو خطأ
$message = '';
$messageType = '';

// معالجة النموذج عند الإرسال
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['name']) && isset($_POST['price']) && isset($_POST['category'])) {
        $name = $_POST['name'];
        $price = $_POST['price'];
        $category = $_POST['category'];
        $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1; // إضافة معالجة حقل الكمية
        $unit = isset($_POST['unit']) ? $_POST['unit'] : 'قطعة'; // إضافة معالجة حقل وحدة القياس
        
        // معالجة الألوان المتعددة
        $productColors = [];
        if (isset($_POST['colors']) && !empty($_POST['colors'])) {
            $productColors = explode(',', $_POST['colors']);
        } else {
            $productColors = ['#000000'];
        }
        
        // تحديث بيانات المنتج
        $productsData[$id]['name'] = $name;
        $productsData[$id]['price'] = $price;
        $productsData[$id]['category'] = $category;
        $productsData[$id]['quantity'] = $quantity; // إضافة تحديث الكمية
        $productsData[$id]['unit'] = $unit; // إضافة تحديث وحدة القياس
        $productsData[$id]['colors'] = $productColors; // تحديث الألوان المتعددة
        
        // حذف الحقل القديم إذا كان موجوداً
        if (isset($productsData[$id]['color'])) {
            unset($productsData[$id]['color']);
        }
        $color = isset($_POST['color']) ? $_POST['color'] : '#000000'; // إضافة معالجة حقل اللون
        
        // التحقق من وجود صورة جديدة
        if (isset($_FILES['image']) && $_FILES['image']['error'] === 0) {
            $oldImage = $productsData[$id]['image'];
            $oldImagePath = $uploadsDir . $oldImage;
            
            // حذف الصورة القديمة إذا كانت موجودة
            if (file_exists($oldImagePath)) {
                unlink($oldImagePath);
            }
            
            // رفع الصورة الجديدة
            $fileName = time() . '_' . basename($_FILES['image']['name']);
            $targetPath = $uploadsDir . $fileName;
            
            if (move_uploaded_file($_FILES['image']['tmp_name'], $targetPath)) {
                $productsData[$id]['image'] = $fileName;
            }
        }
        
        // حفظ البيانات المحدثة
        file_put_contents($dataFile, json_encode($productsData, JSON_PRETTY_PRINT));
        
        // إعداد رسالة النجاح
        $message = 'تم تحديث المنتج بنجاح!';
        $messageType = 'success';
        
        // تحديث متغير المنتج بعد التحديث
        $product = $productsData[$id];
    }
}
?>
<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل المنتج - <?php echo $product['name']; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 0;
            margin: 0;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem 0;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .header h1 {
            font-size: 2.2rem;
            margin: 0;
            font-weight: 600;
        }
        
        .back-link {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            font-size: 1rem;
            transition: var(--transition);
        }
        
        .back-link:hover {
            opacity: 0.8;
        }
        
        .back-link i {
            margin-left: 5px;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 0 20px 50px;
        }
        
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
            overflow: hidden;
            transition: var(--transition);
        }
        
        .card:hover {
            box-shadow: 0 10px 20px rgba(0,0,0,0.15);
        }
        
        .card-header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem 1.5rem;
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        
        .card-header i {
            margin-left: 10px;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        input[type="text"], 
        input[type="number"], 
        input[type="file"],
        select {
            width: 100%;
            padding: 0.8rem 1rem;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: var(--transition);
        }
        
        input[type="text"]:focus, 
        input[type="number"]:focus,
        select:focus {
            border-color: var(--secondary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }
        
        .btn {
            display: inline-block;
            background-color: var(--secondary-color);
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            text-align: center;
            transition: var(--transition);
            text-decoration: none;
        }
        
        .btn:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background-color: var(--secondary-color);
        }
        
        .btn-success {
            background-color: var(--success-color);
        }
        
        .btn-danger {
            background-color: var(--danger-color);
        }
        
        .current-image {
            margin-bottom: 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            background-color: #f8f9fa;
            padding: 20px;
        }
        
        .current-image p {
            margin-bottom: 15px;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .current-image img {
            max-width: 300px;
            max-height: 300px;
            border-radius: var(--border-radius);
            transition: var(--transition);
            object-fit: contain;
        }
        
        .current-image img:hover {
            transform: scale(1.05);
        }
        
        .image-preview {
            display: none;
            margin-top: 15px;
            text-align: center;
        }
        
        .image-preview img {
            max-width: 300px;
            max-height: 300px;
            border-radius: var(--border-radius);
            border: 2px dashed var(--secondary-color);
            padding: 5px;
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            text-align: center;
            padding: 1.5rem 0;
            margin-top: 3rem;
        }
        
        .color-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        
        .color-item {
            display: flex;
            align-items: center;
            gap: 5px;
            background-color: #f8f9fa;
            padding: 5px 10px;
            border-radius: var(--border-radius);
            border: 1px solid #ddd;
        }
        
        .color-item input[type="color"] {
            width: 30px;
            height: 30px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            padding: 0;
            background: none;
        }
        
        .remove-color {
            cursor: pointer;
            color: var(--danger-color);
            margin-right: 5px;
            transition: var(--transition);
        }
        
        .remove-color:hover {
            transform: scale(1.2);
        }
        
        .add-color-btn {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            background-color: var(--light-color);
            padding: 5px 10px;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            color: var(--dark-color);
            font-weight: 500;
        }
        
        .add-color-btn:hover {
            background-color: #d1d8e0;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 0;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: var(--border-radius);
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .alert-success {
            background-color: var(--success-color);
        }
        
        .alert-danger {
            background-color: var(--danger-color);
        }
        
        .alert i {
            margin-left: 10px;
        }
        
        .alert .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            opacity: 0.7;
            transition: var(--transition);
        }
        
        .alert .close-btn:hover {
            opacity: 1;
        }
        
        .product-info-card {
            background-color: #f8f9fa;
            border-radius: var(--border-radius);
            padding: 15px;
            margin-bottom: 20px;
            border-right: 4px solid var(--secondary-color);
        }
        
        .product-info-card h3 {
            margin-bottom: 10px;
            color: var(--dark-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .product-info-card p {
            margin: 5px 0;
            color: #666;
        }
        
        .product-info-card p strong {
            color: var(--dark-color);
        }
        
        .custom-file-upload {
            display: inline-block;
            padding: 10px 15px;
            cursor: pointer;
            background-color: var(--light-color);
            border-radius: var(--border-radius);
            transition: var(--transition);
            text-align: center;
            width: 100%;
        }
        
        .custom-file-upload:hover {
            background-color: #d1d8e0;
        }
        
        .custom-file-upload i {
            margin-left: 5px;
        }
        
        input[type="file"] {
            display: none;
        }
        
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }
            
            .back-link {
                position: static;
                margin-bottom: 10px;
                transform: none;
                display: inline-block;
                color: var(--secondary-color);
            }
            
            .header {
                padding-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="index.php" class="back-link"><i class="fas fa-arrow-right"></i> العودة للرئيسية</a>
        <h1>تعديل المنتج</h1>
    </div>
    
    <div class="container">
        <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?>">
            <span><i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-circle'; ?>"></i> <?php echo $message; ?></span>
            <button type="button" class="close-btn" onclick="this.parentElement.style.display='none'">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <?php endif; ?>
        
        <div class="product-info-card">
            <h3><i class="fas fa-info-circle"></i> معلومات المنتج</h3>
            <p><strong>رقم المنتج:</strong> <?php echo $id; ?></p>
            <p><strong>تاريخ الإضافة:</strong> <?php echo isset($product['date_added']) ? $product['date_added'] : 'غير محدد'; ?></p>
            <p><strong>آخر تحديث:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>
        
        <div class="card">
            <div class="card-header">
                <i class="fas fa-edit"></i> تعديل بيانات المنتج
            </div>
            <div class="card-body">
                <form action="edit.php?id=<?php echo $id; ?>" method="post" enctype="multipart/form-data" id="edit-form">
                    <div class="current-image">
                        <p><i class="fas fa-image"></i> الصورة الحالية</p>
                        <img src="<?php echo $uploadsDir . $product['image']; ?>" alt="<?php echo $product['name']; ?>" id="current-product-image">
                    </div>
                    
                    <div class="form-group">
                        <label for="image"><i class="fas fa-image"></i> تغيير الصورة (اختياري)</label>
                        <label for="image" class="custom-file-upload">
                            <i class="fas fa-upload"></i> اختر صورة جديدة
                        </label>
                        <input type="file" name="image" id="image" accept="image/*" onchange="previewImage(this)">
                        <div class="image-preview" id="image-preview">
                            <p>معاينة الصورة الجديدة:</p>
                            <img src="" alt="معاينة" id="preview-img">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name"><i class="fas fa-tag"></i> اسم المنتج</label>
                            <input type="text" name="name" id="name" value="<?php echo $product['name']; ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="price"><i class="fas fa-money-bill-wave"></i> سعر المنتج</label>
                            <input type="number" name="price" id="price" step="0.01" value="<?php echo $product['price']; ?>" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="quantity"><i class="fas fa-cubes"></i> الكمية</label>
                            <input type="number" name="quantity" id="quantity" min="1" value="<?php echo isset($product['quantity']) ? $product['quantity'] : 1; ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="unit"><i class="fas fa-box"></i> وحدة القياس</label>
                            <select name="unit" id="unit" required>
                                <option value="قطعة" <?php echo (isset($product['unit']) && $product['unit'] == 'قطعة') ? 'selected' : ''; ?>>قطعة</option>
                                <option value="كرتونة" <?php echo (isset($product['unit']) && $product['unit'] == 'كرتونة') ? 'selected' : ''; ?>>كرتونة</option>
                                <option value="دزينة" <?php echo (isset($product['unit']) && $product['unit'] == 'دزينة') ? 'selected' : ''; ?>>دزينة</option>
                                <option value="علبة" <?php echo (isset($product['unit']) && $product['unit'] == 'علبة') ? 'selected' : ''; ?>>علبة</option>
                                <option value="طقم" <?php echo (isset($product['unit']) && $product['unit'] == 'طقم') ? 'selected' : ''; ?>>طقم</option>
                                <option value="حزمة" <?php echo (isset($product['unit']) && $product['unit'] == 'حزمة') ? 'selected' : ''; ?>>حزمة</option>
                                <option value="متر" <?php echo (isset($product['unit']) && $product['unit'] == 'متر') ? 'selected' : ''; ?>>متر</option>
                                <option value="كيلو" <?php echo (isset($product['unit']) && $product['unit'] == 'كيلو') ? 'selected' : ''; ?>>كيلو</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="category"><i class="fas fa-folder"></i> القسم</label>
                        <select name="category" id="category" required>
                            <option value="شناتي" <?php echo ($product['category'] == 'شناتي') ? 'selected' : ''; ?>>شناتي</option>
                            <option value="مطرات" <?php echo ($product['category'] == 'مطرات') ? 'selected' : ''; ?>>مطرات</option>
                            <option value="tornado" <?php echo ($product['category'] == 'tornado') ? 'selected' : ''; ?>>tornado</option>
                            <option value="Pensan & Fatih" <?php echo ($product['category'] == 'Pensan & Fatih') ? 'selected' : ''; ?>>Pensan & Fatih</option>
                            <option value="العاب التحدي" <?php echo ($product['category'] == 'العاب التحدي') ? 'selected' : ''; ?>>العاب التحدي</option>
                            <option value="مستلزمات تعليمية" <?php echo ($product['category'] == 'مستلزمات تعليمية') ? 'selected' : ''; ?>>مستلزمات تعليمية</option>
                            <option value="قرطاسية" <?php echo ($product['category'] == 'قرطاسية') ? 'selected' : ''; ?>>قرطاسية</option>
                            <option value="مقالم" <?php echo ($product['category'] == 'مقالم') ? 'selected' : ''; ?>>مقالم</option>
                            <option value="صناديق الهدايا" <?php echo ($product['category'] == 'صناديق الهدايا') ? 'selected' : ''; ?>>صناديق الهدايا</option>
                            <option value="الرسم و الفنون" <?php echo ($product['category'] == 'الرسم و الفنون') ? 'selected' : ''; ?>>الرسم و الفنون</option>
                            <option value="اكياس الهدايا" <?php echo ($product['category'] == 'اكياس الهدايا') ? 'selected' : ''; ?>>اكياس الهدايا</option>
                            <option value="أخرى" <?php echo ($product['category'] == 'أخرى' || !isset($product['category'])) ? 'selected' : ''; ?>>أخرى</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label><i class="fas fa-palette"></i> ألوان المنتج</label>
                        <div class="color-container" id="product-colors-container">
                            <?php 
                            $productColors = [];
                            if (isset($product['colors']) && is_array($product['colors'])) {
                                $productColors = $product['colors'];
                            } elseif (isset($product['color'])) {
                                $productColors = [$product['color']];
                            } else {
                                $productColors = ['#000000'];
                            }
                            
                            foreach ($productColors as $color): 
                            ?>
                            <div class="color-item">
                                <input type="color" value="<?php echo $color; ?>" onchange="updateColorsInput()">
                                <span class="remove-color" onclick="removeColor(this)"><i class="fas fa-times"></i></span>
                            </div>
                            <?php endforeach; ?>
                            <span class="add-color-btn" onclick="addColor()"><i class="fas fa-plus"></i> إضافة لون</span>
                        </div>
                        <input type="hidden" name="colors" id="colors-input" value="<?php echo implode(',', $productColors); ?>">
                    </div>
                    
                    <div class="action-buttons">
                        <a href="index.php" class="btn btn-danger"><i class="fas fa-times"></i> إلغاء</a>
                        <button type="submit" class="btn btn-success"><i class="fas fa-save"></i> حفظ التغييرات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>جميع الحقوق محفوظة &copy; 2023 - نظام إدارة المنتجات</p>
    </div>
    <script>
        // إضافة لون جديد
        function addColor() {
            const colorContainer = document.getElementById('product-colors-container');
            const addButton = colorContainer.querySelector('.add-color-btn');
            const colorItem = document.createElement('div');
            colorItem.className = 'color-item';
            colorItem.innerHTML = `
                <input type="color" value="#000000" onchange="updateColorsInput()">
                <span class="remove-color" onclick="removeColor(this)"><i class="fas fa-times"></i></span>
            `;
            colorContainer.insertBefore(colorItem, addButton);
            updateColorsInput();
        }
        
        // إزالة لون
        function removeColor(element) {
            const colorItem = element.parentElement;
            const colorContainer = document.getElementById('product-colors-container');
            const colorInputs = colorContainer.querySelectorAll('input[type="color"]');
            
            // لا تسمح بإزالة اللون إذا كان هناك لون واحد فقط
            if (colorInputs.length > 1) {
                colorItem.remove();
                updateColorsInput();
            } else {
                alert('يجب أن يكون هناك لون واحد على الأقل!');
            }
        }
        
        // تحديث حقل الألوان المخفي
        function updateColorsInput() {
            const colorContainer = document.getElementById('product-colors-container');
            const colorInputs = colorContainer.querySelectorAll('input[type="color"]');
            const colors = Array.from(colorInputs).map(input => input.value);
            document.getElementById('colors-input').value = colors.join(',');
        }
        
        // معاينة الصورة قبل الرفع
        function previewImage(input) {
            const preview = document.getElementById('image-preview');
            const previewImg = document.getElementById('preview-img');
            
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    preview.style.display = 'block';
                }
                
                reader.readAsDataURL(input.files[0]);
            } else {
                preview.style.display = 'none';
            }
        }
        
        // التحقق من النموذج قبل الإرسال
        document.getElementById('edit-form').addEventListener('submit', function(event) {
            const nameInput = document.getElementById('name');
            const priceInput = document.getElementById('price');
            const quantityInput = document.getElementById('quantity');
            
            let isValid = true;
            let errorMessage = '';
            
            // التحقق من اسم المنتج
            if (nameInput.value.trim() === '') {
                errorMessage = 'يرجى إدخال اسم المنتج';
                isValid = false;
            }
            
            // التحقق من سعر المنتج
            else if (priceInput.value <= 0) {
                errorMessage = 'يجب أن يكون سعر المنتج أكبر من صفر';
                isValid = false;
            }
            
            // التحقق من كمية المنتج
            else if (quantityInput.value < 0) {
                errorMessage = 'يجب أن تكون كمية المنتج صفر أو أكثر';
                isValid = false;
            }
            
            if (!isValid) {
                event.preventDefault();
                alert(errorMessage);
            }
        });
        
        // إغلاق رسائل التنبيه تلقائياً بعد 5 ثوانٍ
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            if (alerts.length > 0) {
                setTimeout(function() {
                    alerts.forEach(alert => {
                        alert.style.opacity = '0';
                        setTimeout(function() {
                            alert.style.display = 'none';
                        }, 500);
                    });
                }, 5000);
            }
        });
    </script>
</body>
</html>

<?php
// في بداية الملف، بعد تعريف المتغيرات الأساسية
$categoriesFile = 'categories_data.json';
$categories = [];

// تحميل الأقسام من الملف
if (file_exists($categoriesFile)) {
    $categories = json_decode(file_get_contents($categoriesFile), true);
}
?>

<!-- في مكان عنصر select في النموذج -->
<div class="form-group">
    <label for="category"><i class="fas fa-folder"></i> القسم</label>
    <select name="category" id="category" required>
        <?php
        if (!empty($categories)) {
            foreach ($categories as $cat) {
                if ($cat['active']) { // عرض الأقسام النشطة فقط
                    $selected = ($product['category'] == $cat['name']) ? 'selected' : '';
                    echo "<option value=\"{$cat['name']}\" {$selected}>{$cat['name']}</option>";
                }
            }
        } else {
            // إذا لم يتم العثور على أقسام، عرض قسم "أخرى" كخيار افتراضي
            echo '<option value="أخرى" selected>أخرى</option>';
        }
        ?>
    </select>
</div>