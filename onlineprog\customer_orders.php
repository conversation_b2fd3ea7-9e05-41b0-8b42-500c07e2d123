<?php
session_start();

// التحقق من تسجيل دخول المستخدم
if (!isset($_SESSION['customer_id'])) {
    header('Location: customer_login.php');
    exit;
}

// تحميل بيانات الطلبيات
$customer_orders = [];
if (file_exists('customer_orders.json')) {
    $all_orders = json_decode(file_get_contents('customer_orders.json'), true);
    
    // تصفية الطلبيات الخاصة بالعميل الحالي
    foreach ($all_orders as $order) {
        if ($order['customer_id'] == $_SESSION['customer_id']) {
            $customer_orders[] = $order;
        }
    }
    
    // ترتيب الطلبيات من الأحدث إلى الأقدم
    usort($customer_orders, function($a, $b) {
        return strtotime($b['order_date']) - strtotime($a['order_date']);
    });
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلبياتي - متجر المنتجات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 0;
            margin: 0;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem 0;
            text-align: center;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.2rem;
            margin: 0;
            font-weight: 600;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px 50px;
        }
        
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            margin-bottom: 20px;
        }
        
        .nav-links {
            display: flex;
            gap: 15px;
        }
        
        .nav-link {
            text-decoration: none;
            color: var(--dark-color);
            font-weight: 600;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .nav-link:hover {
            color: var(--secondary-color);
        }
        
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .card-header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem 1.5rem;
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .card-header i {
            margin-left: 10px;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .empty-orders {
            text-align: center;
            padding: 2rem;
        }
        
        .empty-orders i {
            font-size: 4rem;
            color: var(--light-color);
            margin-bottom: 1rem;
        }
        
        .empty-orders p {
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
        }
        
        .order-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .order-card {
            border: 1px solid #eee;
            border-radius: var(--border-radius);
            overflow: hidden;
            transition: var(--transition);
        }
        
        .order-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-3px);
        }
        
        .order-header {
            background-color: var(--secondary-color);
            color: white;
            padding: 0.8rem 1rem;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .order-body {
            padding: 1rem;
        }
        
        .order-info {
            margin-bottom: 1rem;
        }
        
        .order-info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
        }
        
        .order-info-label {
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .order-status {
            display: inline-block;
            padding: 0.3rem 0.6rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-pending {
            background-color: var(--warning-color);
            color: white;
        }
        
        .status-processing {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .status-shipped {
            background-color: var(--primary-color);
            color: white;
        }
        
        .status-delivered {
            background-color: var(--success-color);
            color: white;
        }
        
        .status-cancelled {
            background-color: var(--danger-color);
            color: white;
        }
        
        .order-total {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-top: 1rem;
            text-align: center;
            padding-top: 1rem;
            border-top: 1px solid #eee;
        }
        
        .view-details-btn {
            display: block;
            width: 100%;
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 0.8rem;
            border-radius: var(--border-radius);
            text-align: center;
            text-decoration: none;
            font-weight: 600;
            margin-top: 1rem;
            transition: var(--transition);
        }
        
        .view-details-btn:hover {
            background-color: #1e2b38;
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            text-align: center;
            padding: 1.5rem 0;
            margin-top: 3rem;
        }
        
        @media (max-width: 768px) {
            .order-list {
                grid-template-columns: 1fr;
            }
            
            .navbar {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>طلبياتي</h1>
    </div>
    
    <div class="container">
        <div class="navbar">
            <div class="nav-links">
                <a href="customer_products.php" class="nav-link"><i class="fas fa-home"></i> الرئيسية</a>
                <a href="customer_cart.php" class="nav-link"><i class="fas fa-shopping-cart"></i> سلة التسوق</a>
            </div>
            
            <a href="customer_logout.php" class="nav-link">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </a>
        </div>
        
        <div class="card">
            <div class="card-header">
                <span><i class="fas fa-box"></i> طلبياتي</span>
            </div>
            <div class="card-body">
                <?php if (empty($customer_orders)): ?>
                    <div class="empty-orders">
                        <i class="fas fa-box-open"></i>
                        <p>لا توجد طلبيات سابقة</p>
                        <a href="customer_products.php" class="view-details-btn">تصفح المنتجات</a>
                    </div>
                <?php else: ?>
                    <div class="order-list">
                        <?php foreach ($customer_orders as $order): ?>
                            <div class="order-card">
                                <div class="order-header">
                                    <span>طلب #<?php echo $order['order_id']; ?></span>
                                    <span><?php echo date('d/m/Y', strtotime($order['order_date'])); ?></span>
                                </div>
                                <div class="order-body">
                                    <div class="order-info">
                                        <div class="order-info-row">
                                            <span class="order-info-label">الحالة:</span>
                                            <span class="order-status status-<?php echo $order['status']; ?>">
                                                <?php 
                                                $status_text = '';
                                                switch($order['status']) {
                                                    case 'pending':
                                                        $status_text = 'قيد المعالجة';
                                                        break;
                                                    case 'processing':
                                                        $status_text = 'جاري التجهيز';
                                                        break;
                                                    case 'shipped':
                                                        $status_text = 'تم الشحن';
                                                        break;
                                                    case 'delivered':
                                                        $status_text = 'تم التسليم';
                                                        break;
                                                    case 'cancelled':
                                                        $status_text = 'ملغي';
                                                        break;
                                                    default:
                                                        $status_text = $order['status'];
                                                }
                                                echo $status_text;
                                                ?>
                                            </span>
                                        </div>
                                        <div class="order-info-row">
                                            <span class="order-info-label">عدد المنتجات:</span>
                                            <span><?php echo count($order['items']); ?> منتج</span>
                                        </div>
                                        <div class="order-info-row">
                                            <span class="order-info-label">طريقة الدفع:</span>
                                            <span>
                                                <?php echo $order['payment_method'] == 'cash' ? 'الدفع عند الاستلام' : 'تحويل بنكي'; ?>
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <div class="order-total">
                                        المجموع: <?php echo $order['total_amount']; ?> شيقل
                                    </div>
                                    
                                    <a href="customer_order_details.php?id=<?php echo $order['order_id']; ?>" class="view-details-btn">
                                        <i class="fas fa-eye"></i> عرض التفاصيل
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>جميع الحقوق محفوظة &copy; <?php echo date('Y'); ?> متجر المنتجات</p>
    </div>
</body>
</html>