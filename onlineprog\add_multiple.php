<?php
// تحميل الفئات من الملف
$categories = [];
if (file_exists('categories_data.json')) {
    $categoriesJson = file_get_contents('categories_data.json');
    $categories = json_decode($categoriesJson, true) ?: [];
} else {
    $categories = [
        ['id' => 1, 'name' => 'قرطاسية'],
        ['id' => 2, 'name' => 'أدوات مكتبية'],
        ['id' => 3, 'name' => 'أدوات مدرسية']
    ];
}

// تصفية الفئات النشطة فقط
$activeCategories = array_filter($categories, function($category) {
    return !isset($category['is_deleted']) || !$category['is_deleted'];
});
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رفع منتجات متعددة</title>
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .upload-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .drag-area {
            border: 2px dashed #ccc;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            border-radius: 8px;
            background: #fafafa;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .drag-area.active {
            border-color: var(--primary-color);
            background: #f0f8ff;
        }

        .preview-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .preview-item {
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 8px;
            background: white;
        }

        .preview-image {
            width: 100%;
            height: 200px;
            object-fit: contain;
            margin-bottom: 10px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input[type="text"],
        input[type="number"],
        select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }

        .color-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .color-item {
            display: flex;
            align-items: center;
            background: #f5f5f5;
            padding: 5px 10px;
            border-radius: 4px;
        }

        .color-preview {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 5px;
            border: 1px solid #ddd;
        }

        .default-controls {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .color-input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .error-message {
            color: var(--danger-color);
            margin-top: 5px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="upload-container">
        <form id="uploadForm" action="multi_upload.php" method="post" enctype="multipart/form-data">
            <div class="default-controls">
                <h3>القيم الافتراضية</h3>
                <div class="form-group">
                    <label>الألوان الافتراضية:</label>
                    <div class="color-input-group">
                        <input type="text" id="defaultColorInput" placeholder="أدخل اسم اللون">
                        <input type="color" id="defaultColorPicker">
                        <button type="button" class="btn btn-primary" onclick="addDefaultColor()">إضافة</button>
                    </div>
                    <div id="defaultColors" class="color-container"></div>
                </div>
                <div class="form-group">
                    <label for="defaultCategory">الفئة الافتراضية:</label>
                    <select id="defaultCategory">
                        <?php foreach ($activeCategories as $category): ?>
                            <option value="<?php echo htmlspecialchars($category['name']); ?>">
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="form-group">
                    <label for="defaultQuantity">الكمية الافتراضية:</label>
                    <input type="number" id="defaultQuantity" value="1" min="1">
                </div>
                <div class="form-group">
                    <label for="defaultUnit">وحدة القياس الافتراضية:</label>
                    <select id="defaultUnit">
                        <option value="قطعة">قطعة</option>
                        <option value="علبة">علبة</option>
                        <option value="طقم">طقم</option>
                    </select>
                </div>
                <button type="button" class="btn btn-primary" onclick="applyDefaultsToAll()">تطبيق على الكل</button>
            </div>

            <div class="drag-area" id="dragArea">
                <h3>اسحب وأفلت الصور هنا أو انقر للاختيار</h3>
                <input type="file" id="fileInput" multiple accept="image/*" style="display: none;">
            </div>

            <div id="previewContainer" class="preview-container"></div>

            <button type="submit" class="btn btn-primary" style="margin-top: 20px;">رفع المنتجات</button>
        </form>
    </div>

    <script>
        const dragArea = document.getElementById('dragArea');
        const fileInput = document.getElementById('fileInput');
        const previewContainer = document.getElementById('previewContainer');
        const uploadForm = document.getElementById('uploadForm');
        let defaultColors = [];

        // تحديد الفئات المتاحة
        const categories = <?php echo json_encode($activeCategories); ?>;

        dragArea.addEventListener('click', () => fileInput.click());

        dragArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            dragArea.classList.add('active');
        });

        dragArea.addEventListener('dragleave', () => {
            dragArea.classList.remove('active');
        });

        dragArea.addEventListener('drop', (e) => {
            e.preventDefault();
            dragArea.classList.remove('active');
            const files = e.dataTransfer.files;
            handleFiles(files);
        });

        fileInput.addEventListener('change', (e) => {
            const files = e.target.files;
            handleFiles(files);
        });

        function handleFiles(files) {
            Array.from(files).forEach((file, index) => {
                if (!file.type.startsWith('image/')) return;

                const reader = new FileReader();
                reader.onload = (e) => {
                    const previewItem = document.createElement('div');
                    previewItem.className = 'preview-item';
                    previewItem.innerHTML = `
                        <img src="${e.target.result}" class="preview-image">
                        <div class="form-group">
                            <label>اسم المنتج:</label>
                            <input type="text" name="names[]" required>
                        </div>
                        <div class="form-group">
                            <label>السعر:</label>
                            <input type="number" name="prices[]" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label>الفئة:</label>
                            <select name="categories[]">
                                ${categories.map(cat => `
                                    <option value="${cat.name}">${cat.name}</option>
                                `).join('')}
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الكمية:</label>
                            <input type="number" name="quantities[]" value="1" min="1" required>
                        </div>
                        <div class="form-group">
                            <label>وحدة القياس:</label>
                            <select name="units[]">
                                <option value="قطعة">قطعة</option>
                                <option value="علبة">علبة</option>
                                <option value="طقم">طقم</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الألوان:</label>
                            <div class="color-input-group">
                                <input type="text" placeholder="أدخل اسم اللون">
                                <input type="color">
                                <button type="button" class="btn btn-primary" onclick="addColor(this)">إضافة</button>
                            </div>
                            <div class="color-container"></div>
                            <input type="hidden" name="colors[]" value="">
                        </div>
                        <button type="button" class="btn btn-danger" onclick="this.parentElement.remove()">حذف</button>
                    `;

                    // إضافة الصورة كحقل مخفي
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'file';
                    hiddenInput.name = 'images[]';
                    hiddenInput.style.display = 'none';
                    hiddenInput.required = true;

                    // إنشاء DataTransfer وإضافة الملف
                    const dataTransfer = new DataTransfer();
                    dataTransfer.items.add(file);
                    hiddenInput.files = dataTransfer.files;

                    previewItem.appendChild(hiddenInput);
                    previewContainer.appendChild(previewItem);
                };
                reader.readAsDataURL(file);
            });
        }

        function addDefaultColor() {
            const colorName = document.getElementById('defaultColorInput').value.trim();
            const colorValue = document.getElementById('defaultColorPicker').value;

            if (colorName) {
                defaultColors.push({ name: colorName, value: colorValue });
                updateDefaultColorDisplay();
                document.getElementById('defaultColorInput').value = '';
            }
        }

        function updateDefaultColorDisplay() {
            const container = document.getElementById('defaultColors');
            container.innerHTML = defaultColors.map((color, index) => `
                <div class="color-item">
                    <div class="color-preview" style="background-color: ${color.value}"></div>
                    ${color.name}
                    <button type="button" class="btn btn-danger" onclick="removeDefaultColor(${index})">×</button>
                </div>
            `).join('');
        }

        function removeDefaultColor(index) {
            defaultColors.splice(index, 1);
            updateDefaultColorDisplay();
        }

        function addColor(button) {
            const container = button.parentElement.parentElement;
            const colorName = container.querySelector('input[type="text"]').value.trim();
            const colorValue = container.querySelector('input[type="color"]').value;
            const colorsContainer = container.querySelector('.color-container');
            const hiddenInput = container.querySelector('input[name="colors[]"]');

            if (colorName) {
                const colors = hiddenInput.value ? JSON.parse(hiddenInput.value) : [];
                colors.push({ name: colorName, value: colorValue });
                hiddenInput.value = JSON.stringify(colors);

                colorsContainer.innerHTML = colors.map((color, index) => `
                    <div class="color-item">
                        <div class="color-preview" style="background-color: ${color.value}"></div>
                        ${color.name}
                        <button type="button" class="btn btn-danger" onclick="removeColor(this, ${index})">×</button>
                    </div>
                `).join('');

                container.querySelector('input[type="text"]').value = '';
            }
        }

        function removeColor(button, index) {
            const container = button.closest('.form-group');
            const hiddenInput = container.querySelector('input[name="colors[]"]');
            const colors = JSON.parse(hiddenInput.value);
            colors.splice(index, 1);
            hiddenInput.value = JSON.stringify(colors);

            const colorsContainer = container.querySelector('.color-container');
            colorsContainer.innerHTML = colors.map((color, idx) => `
                <div class="color-item">
                    <div class="color-preview" style="background-color: ${color.value}"></div>
                    ${color.name}
                    <button type="button" class="btn btn-danger" onclick="removeColor(this, ${idx})">×</button>
                </div>
            `).join('');
        }

        function applyDefaultsToAll() {
            const defaultCategory = document.getElementById('defaultCategory').value;
            const defaultQuantity = document.getElementById('defaultQuantity').value;
            const defaultUnit = document.getElementById('defaultUnit').value;

            document.querySelectorAll('.preview-item').forEach(item => {
                item.querySelector('select[name="categories[]"]').value = defaultCategory;
                item.querySelector('input[name="quantities[]"]').value = defaultQuantity;
                item.querySelector('select[name="units[]"]').value = defaultUnit;

                const colorsContainer = item.querySelector('.color-container');
                const hiddenInput = item.querySelector('input[name="colors[]"]');
                hiddenInput.value = JSON.stringify(defaultColors);

                colorsContainer.innerHTML = defaultColors.map((color, index) => `
                    <div class="color-item">
                        <div class="color-preview" style="background-color: ${color.value}"></div>
                        ${color.name}
                        <button type="button" class="btn btn-danger" onclick="removeColor(this, ${index})">×</button>
                    </div>
                `).join('');
            });
        }
    </script>
</body>
</html>