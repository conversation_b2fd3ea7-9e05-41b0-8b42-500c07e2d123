<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رفع مجموعة أصناف دفعة واحدة - نظام إدارة المنتجات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 0;
            margin: 0;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem 0;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.2rem;
            margin: 0;
            font-weight: 600;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px 50px;
        }
        
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .card-header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem 1.5rem;
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .card-header i {
            margin-left: 10px;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1.2rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        input[type="text"], 
        input[type="number"], 
        input[type="file"],
        select {
            width: 100%;
            padding: 0.8rem 1rem;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: var(--transition);
        }
        
        input[type="text"]:focus, 
        input[type="number"]:focus,
        select:focus {
            border-color: var(--secondary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }
        
        .btn {
            display: inline-block;
            background-color: var(--secondary-color);
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            text-align: center;
            transition: var(--transition);
            text-decoration: none;
        }
        
        .btn:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background-color: var(--secondary-color);
        }
        
        .btn-success {
            background-color: var(--success-color);
        }
        
        .btn-danger {
            background-color: var(--danger-color);
        }
        
        .btn-warning {
            background-color: var(--warning-color);
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
        
        .csv-template {
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .csv-template a {
            color: var(--secondary-color);
            text-decoration: none;
            font-weight: 600;
        }
        
        .csv-template a:hover {
            text-decoration: underline;
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            text-align: center;
            padding: 1.5rem 0;
            margin-top: 3rem;
        }
        
        .back-link {
            display: inline-block;
            margin-bottom: 1rem;
            color: var(--secondary-color);
            text-decoration: none;
            font-weight: 600;
        }
        
        .back-link i {
            margin-left: 5px;
        }
        
        .back-link:hover {
            color: #2980b9;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>رفع مجموعة أصناف دفعة واحدة</h1>
    </div>
    
    <div class="container">
        <a href="index.php" class="back-link"><i class="fas fa-arrow-right"></i> العودة إلى الصفحة الرئيسية</a>
        
        <div class="card">
            <div class="card-header">
                <span><i class="fas fa-file-import"></i> رفع مجموعة أصناف دفعة واحدة</span>
            </div>
            <div class="card-body">
                <form action="bulk_upload.php" method="post" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="csv_file"><i class="fas fa-file-csv"></i> ملف CSV يحتوي على بيانات المنتجات:</label>
                        <input type="file" name="csv_file" accept=".csv" required>
                        <div class="csv-template">
                            <a href="template.csv" download><i class="fas fa-download"></i> تحميل قالب ملف CSV</a>
                            <p>يجب أن يحتوي على الأعمدة: اسم_الصورة، اسم_الصنف، السعر، القسم</p>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="images_zip"><i class="fas fa-file-archive"></i> ملف ZIP يحتوي على صور المنتجات:</label>
                        <input type="file" name="images_zip" accept=".zip" required>
                    </div>
                    
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-upload"></i> رفع المنتجات بالجملة
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>جميع الحقوق محفوظة &copy; 2023 - نظام إدارة المنتجات</p>
    </div>
</body>
</html>