<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلبية مبيعات - نظام إدارة المنتجات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 0;
            margin: 0;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem 0;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.2rem;
            margin: 0;
            font-weight: 600;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px 50px;
        }
        
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .card-header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem 1.5rem;
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .card-header i {
            margin-left: 10px;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1.2rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        input[type="text"], 
        input[type="number"], 
        select {
            width: 100%;
            padding: 0.8rem 1rem;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: var(--transition);
        }
        
        input[type="text"]:focus, 
        input[type="number"]:focus,
        select:focus {
            border-color: var(--secondary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }
        
        .btn {
            display: inline-block;
            background-color: var(--secondary-color);
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            text-align: center;
            transition: var(--transition);
            text-decoration: none;
        }
        
        .btn:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background-color: var(--secondary-color);
        }
        
        .btn-success {
            background-color: var(--success-color);
        }
        
        .btn-danger {
            background-color: var(--danger-color);
        }
        
        .btn-warning {
            background-color: var(--warning-color);
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
        
        .product-form {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr auto;
            gap: 1rem;
            align-items: end;
            margin-bottom: 1.5rem;
        }
        
        @media (max-width: 992px) {
            .product-form {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        @media (max-width: 576px) {
            .product-form {
                grid-template-columns: 1fr;
            }
        }
        
        .product-list {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1.5rem;
        }
        
        .product-list th, 
        .product-list td {
            padding: 0.75rem;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }
        
        .product-list th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .product-list tr:hover {
            background-color: #f8f9fa;
        }
        
        .empty-products {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
            font-style: italic;
            background-color: #f8f9fa;
            border-radius: var(--border-radius);
            margin-top: 1.5rem;
        }
        
        .totals {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .total-item {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: var(--border-radius);
            text-align: center;
        }
        
        .total-item h3 {
            margin-bottom: 0.5rem;
            color: var(--dark-color);
        }
        
        .total-item p {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }
        
        .total-cost p {
            color: var(--danger-color);
        }
        
        .total-selling p {
            color: var(--secondary-color);
        }
        
        .total-profit p {
            color: var(--success-color);
        }
        
        .action-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 1.5rem;
        }
        
        @media (max-width: 576px) {
            .action-buttons {
                flex-direction: column;
                gap: 1rem;
            }
            
            .action-buttons .btn {
                width: 100%;
            }
            
            .totals {
                grid-template-columns: 1fr;
            }
        }
        
        @media print {
            .header, .card-header, .product-form, .action-buttons, .btn-delete {
                display: none;
            }
            
            body, .container, .card, .card-body {
                background: white;
                padding: 0;
                margin: 0;
                box-shadow: none;
            }
            
            .card {
                border: none;
            }
            
            .product-list, .totals {
                page-break-inside: avoid;
            }
            
            .product-list th, .product-list td {
                border-color: #000;
            }
        }
        
        /* إضافة أنماط للسحب والإفلات */
        .drop-zone {
            border: 2px dashed #ccc;
            border-radius: var(--border-radius);
            padding: 2rem;
            text-align: center;
            margin-bottom: 1.5rem;
            transition: var(--transition);
            background-color: #f8f9fa;
            cursor: pointer;
        }
        
        .drop-zone.active {
            border-color: var(--secondary-color);
            background-color: rgba(52, 152, 219, 0.1);
        }
        
        .drop-zone p {
            margin: 0;
            color: #6c757d;
        }
        
        .drop-zone i {
            font-size: 2rem;
            color: #6c757d;
            margin-bottom: 1rem;
        }
        
        .preview-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .image-preview {
            background-color: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
            padding: 0.8rem;
            position: relative;
        }
        
        .image-preview img {
            width: 100%;
            height: 100px;
            object-fit: cover;
            border-radius: calc(var(--border-radius) - 4px);
            margin-bottom: 0.8rem;
        }
        
        .image-preview input {
            margin-bottom: 0.8rem;
            font-size: 0.9rem;
        }
        
        .image-preview .btn-add-to-order {
            width: 100%;
            padding: 0.5rem;
            font-size: 0.8rem;
        }
        
        .image-preview .btn-remove {
            position: absolute;
            top: 5px;
            right: 5px;
            background-color: rgba(231, 76, 60, 0.8);
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>طلبية مبيعات</h1>
    </div>
    
    <div class="container">
        <div class="card">
            <div class="card-header">
                <span><i class="fas fa-shopping-cart"></i> إنشاء طلبية جديدة</span>
                <a href="index.php" class="btn btn-sm btn-primary"><i class="fas fa-home"></i> الرئيسية</a>
            </div>
            <div class="card-body">
                <!-- نموذج إضافة منتج -->
                <form id="add-product-form" class="product-form">
                    <div class="form-group">
                        <label for="barcode">الباركود</label>
                        <input type="text" id="barcode" name="barcode" placeholder="أدخل الباركود أو اتركه فارغًا للإنشاء التلقائي">
                    </div>
                    <div class="form-group">
                        <label for="product_name">اسم المنتج</label>
                        <input type="text" id="product_name" name="product_name" placeholder="أدخل اسم المنتج" required>
                    </div>
                    <div class="form-group">
                        <label for="cost_price">سعر التكلفة</label>
                        <input type="number" id="cost_price" name="cost_price" step="0.01" min="0" placeholder="0.00" required>
                    </div>
                    <div class="form-group">
                        <label for="selling_price">سعر البيع</label>
                        <input type="number" id="selling_price" name="selling_price" step="0.01" min="0" placeholder="0.00" required>
                    </div>
                    <div class="form-group">
                        <label for="quantity">الكمية</label>
                        <input type="number" id="quantity" name="quantity" min="1" value="1" required>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-success"><i class="fas fa-plus"></i> إضافة للطلبية</button>
                    </div>
                </form>
                
                <!-- منطقة السحب والإفلات -->
                <div class="drop-zone" id="drop-zone">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>اسحب وأفلت صور المنتجات هنا أو انقر للاختيار</p>
                    <input type="file" id="fileElem" accept="image/*" multiple style="display:none">
                </div>
                
                <!-- منطقة عرض الصور -->
                <div id="preview-container" class="preview-container"></div>
                
                <!-- أزرار الإجراءات العلوية -->
                <div class="action-buttons-top">
                    <button id="clear-all" class="btn btn-danger"><i class="fas fa-trash-alt"></i> مسح الكل</button>
                    <button id="cancel" class="btn btn-warning"><i class="fas fa-times"></i> إلغاء</button>
                </div>
                
                <!-- قائمة المنتجات -->
                <div id="product-list-container">
                    <table class="product-list" id="product-list">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الباركود</th>
                                <th>اسم المنتج</th>
                                <th>سعر التكلفة</th>
                                <th>سعر البيع</th>
                                <th>الكمية</th>
                                <th>إجمالي التكلفة</th>
                                <th>إجمالي البيع</th>
                                <th>الربح</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="product-items">
                            <!-- سيتم إضافة المنتجات هنا بواسطة JavaScript -->
                        </tbody>
                    </table>
                    <div id="empty-products" class="empty-products">
                        لا توجد منتجات في الطلبية حاليًا
                    </div>
                </div>
                
                <!-- إجماليات الطلبية -->
                <div class="totals">
                    <div class="total-item total-cost">
                        <h3>إجمالي التكلفة</h3>
                        <p id="total-cost">0.00</p>
                    </div>
                    <div class="total-item total-selling">
                        <h3>إجمالي البيع</h3>
                        <p id="total-selling">0.00</p>
                    </div>
                    <div class="total-item total-profit">
                        <h3>إجمالي الربح المتوقع</h3>
                        <p id="total-profit">0.00</p>
                    </div>
                </div>
                
                <!-- أزرار الإجراءات -->
                <div class="action-buttons">
                    <button id="print-order" class="btn btn-primary"><i class="fas fa-print"></i> طباعة الطلبية</button>
                    <button id="save-order" class="btn btn-success"><i class="fas fa-save"></i> حفظ الطلبية</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // انتظار تحميل الصفحة بالكامل
        document.addEventListener('DOMContentLoaded', function() {
            // تحديد العناصر
            const addProductForm = document.getElementById('add-product-form');
            const productItems = document.getElementById('product-items');
            const emptyProducts = document.getElementById('empty-products');
            const productList = document.getElementById('product-list');
            const totalCost = document.getElementById('total-cost');
            const totalSelling = document.getElementById('total-selling');
            const totalProfit = document.getElementById('total-profit');
            const clearAllBtn = document.getElementById('clear-all');
            const cancelBtn = document.getElementById('cancel');
            const printOrderBtn = document.getElementById('print-order');
            const saveOrderBtn = document.getElementById('save-order');
            const dropZone = document.getElementById('drop-zone');
            
            // منطقة السحب والإفلات
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, preventDefaults, false);
            });
            
            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            ['dragenter', 'dragover'].forEach(eventName => {
                dropZone.addEventListener(eventName, highlight, false);
            });
            
            ['dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, unhighlight, false);
            });
            
            function highlight() {
                dropZone.classList.add('active');
            }
            
            function unhighlight() {
                dropZone.classList.remove('active');
            }
            
            dropZone.addEventListener('drop', handleDrop, false);
            dropZone.addEventListener('click', () => document.getElementById('fileElem').click(), false);
            document.getElementById('fileElem').addEventListener('change', function() {
                handleFiles(this.files);
            });
            
            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;
                handleFiles(files);
            }
            
            function handleFiles(files) {
                const previewContainer = document.getElementById('preview-container');
                
                if (files.length > 0) {
                    Array.from(files).forEach((file, index) => {
                        if (file.type.startsWith('image/')) {
                            const reader = new FileReader();
                            reader.onload = function(e) {
                                const preview = document.createElement('div');
                                preview.className = 'image-preview';
                                preview.innerHTML = `
                                    <button type="button" class="btn-remove"><i class="fas fa-times"></i></button>
                                    <img src="${e.target.result}" alt="Preview">
                                    <input type="text" class="product-name" placeholder="اسم المنتج" required>
                                    <input type="number" class="cost-price" placeholder="سعر التكلفة" step="0.01" min="0" required>
                                    <input type="number" class="selling-price" placeholder="سعر البيع" step="0.01" min="0" required>
                                    <input type="number" class="quantity" placeholder="الكمية" min="1" value="1" required>
                                    <button type="button" class="btn btn-sm btn-success btn-add-to-order">إضافة للطلبية</button>
                                `;
                                previewContainer.appendChild(preview);
                                
                                // إضافة مستمع حدث لزر الإضافة للطلبية
                                const addButton = preview.querySelector('.btn-add-to-order');
                                addButton.addEventListener('click', function() {
                                    console.log('تم النقر على زر إضافة للطلبية');
                                    const nameInput = preview.querySelector('.product-name');
                                    const costInput = preview.querySelector('.cost-price');
                                    const sellingInput = preview.querySelector('.selling-price');
                                    const quantityInput = preview.querySelector('.quantity');
                                    
                                    const name = nameInput.value;
                                    const costPrice = parseFloat(costInput.value);
                                    const sellingPrice = parseFloat(sellingInput.value);
                                    const quantity = parseInt(quantityInput.value) || 1;
                                    
                                    if (name && !isNaN(costPrice) && !isNaN(sellingPrice)) {
                                        const product = {
                                            id: Date.now(),
                                            barcode: generateBarcode(),
                                            name: name,
                                            costPrice: costPrice,
                                            sellingPrice: sellingPrice,
                                            quantity: quantity,
                                            totalCost: costPrice * quantity,
                                            totalSelling: sellingPrice * quantity,
                                            profit: (sellingPrice - costPrice) * quantity,
                                            image: e.target.result
                                        };
                                        
                                        products.push(product);
                                        saveProducts();
                                        renderProducts();
                                        updateTotals();
                                        
                                        // إزالة العنصر بعد إضافته للطلبية
                                        preview.remove();
                                    } else {
                                        alert('الرجاء ملء جميع الحقول المطلوبة بشكل صحيح');
                                    }
                                });
                                
                                // إضافة مستمع حدث لزر الإزالة
                                preview.querySelector('.btn-remove').addEventListener('click', function() {
                                    preview.remove();
                                });
                            };
                            reader.readAsDataURL(file);
                        }
                    });
                }
            }
            
            // مصفوفة لتخزين المنتجات
            let products = [];
            
            // استرجاع المنتجات من التخزين المحلي إذا كانت موجودة
            if (localStorage.getItem('salesOrderProducts')) {
                products = JSON.parse(localStorage.getItem('salesOrderProducts'));
                renderProducts();
                updateTotals();
            }
            
            // دالة لإنشاء باركود تلقائي
            function generateBarcode() {
                // إنشاء باركود من 13 رقم (مشابه لمعيار EAN-13)
                let barcode = '';
                // إضافة رقم البلد (مثلاً 6 للسعودية)
                barcode += '6';
                // إضافة 11 رقم عشوائي
                for (let i = 0; i < 11; i++) {
                    barcode += Math.floor(Math.random() * 10);
                }
                // إضافة رقم التحقق (يمكن تحسينه لاحقاً لحساب رقم التحقق الفعلي)
                barcode += Math.floor(Math.random() * 10);
                return barcode;
            }
            
            // إضافة منتج جديد
            addProductForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const barcode = document.getElementById('barcode').value;
                const name = document.getElementById('product_name').value;
                const costPrice = parseFloat(document.getElementById('cost_price').value);
                const sellingPrice = parseFloat(document.getElementById('selling_price').value);
                const quantity = parseInt(document.getElementById('quantity').value) || 1;
                
                if (name && !isNaN(costPrice) && !isNaN(sellingPrice)) {
                    const product = {
                        id: Date.now(),
                        barcode: barcode || generateBarcode(),
                        name: name,
                        costPrice: costPrice,
                        sellingPrice: sellingPrice,
                        quantity: quantity,
                        totalCost: costPrice * quantity,
                        totalSelling: sellingPrice * quantity,
                        profit: (sellingPrice - costPrice) * quantity
                    };
                    
                    products.push(product);
                    saveProducts();
                    renderProducts();
                    updateTotals();
                    
                    // إعادة تعيين النموذج
                    addProductForm.reset();
                    document.getElementById('barcode').focus();
                }
            });
            
            // حذف منتج
            function deleteProduct(id) {
                products = products.filter(product => product.id !== id);
                saveProducts();
                renderProducts();
                updateTotals();
            }
            
            // مسح جميع المنتجات
            clearAllBtn.addEventListener('click', function() {
                if (products.length === 0) {
                    alert('لا توجد منتجات لمسحها!');
                    return;
                }
                
                if (confirm('هل أنت متأكد من مسح جميع المنتجات من الطلبية؟')) {
                    products = [];
                    saveProducts();
                    renderProducts();
                    updateTotals();
                }
            });
            
            // إلغاء وإعادة توجيه للصفحة الرئيسية
            cancelBtn.addEventListener('click', function() {
                if (products.length > 0) {
                    if (confirm('هل أنت متأكد من إلغاء الطلبية؟ سيتم فقدان جميع البيانات غير المحفوظة.')) {
                        window.location.href = 'index.php';
                    }
                } else {
                    window.location.href = 'index.php';
                }
            });
            
            // طباعة الطلبية
            printOrderBtn.addEventListener('click', function() {
                if (products.length === 0) {
                    alert('لا يمكن طباعة طلبية فارغة. الرجاء إضافة منتجات أولاً.');
                    return;
                }
                
                window.print();
            });
            
            // حفظ الطلبية
            saveOrderBtn.addEventListener('click', function() {
                if (products.length === 0) {
                    alert('لا يمكن حفظ طلبية فارغة. الرجاء إضافة منتجات أولاً.');
                    return;
                }
                
                // تجميع بيانات الطلبية
                const orderData = {
                    products: products,
                    total_cost: parseFloat(totalCost.textContent),
                    total_selling: parseFloat(totalSelling.textContent),
                    total_profit: parseFloat(totalProfit.textContent)
                };
                
                console.log('بيانات الطلبية المراد حفظها:', orderData);
                
                // إرسال البيانات إلى الخادم
                fetch('save_order.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'order_data=' + encodeURIComponent(JSON.stringify(orderData))
                })
                .then(response => {
                    console.log('استجابة الخادم:', response);
                    if (!response.ok) {
                        throw new Error('فشل الاتصال بالخادم. الرمز: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('بيانات الاستجابة:', data);
                    if (data.success) {
                        alert('تم حفظ الطلبية بنجاح! رقم الطلبية: ' + data.order_id);
                        // إعادة تعيين الطلبية بعد الحفظ
                        products = [];
                        saveProducts();
                        renderProducts();
                        updateTotals();
                    } else {
                        alert('حدث خطأ أثناء حفظ الطلبية: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('خطأ:', error);
                    alert('حدث خطأ أثناء حفظ الطلبية: ' + error.message);
                });
            });
            
            // عرض المنتجات
            function renderProducts() {
                if (products.length === 0) {
                    productList.style.display = 'none';
                    emptyProducts.style.display = 'block';
                } else {
                    productList.style.display = 'table';
                    emptyProducts.style.display = 'none';
                    
                    // مسح جميع المنتجات الحالية
                    productItems.innerHTML = '';
                    
                    // إضافة المنتجات
                    products.forEach((product, index) => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${index + 1}</td>
                            <td>${product.barcode}</td>
                            <td>
                                ${product.image ? `<img src="${product.image}" alt="${product.name}" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px; pointer-events: none; margin-left: 8px;">` : ''}
                                <span style="pointer-events: none;">${product.name}</span>
                            </td>
                            <td>${product.costPrice.toFixed(2)}</td>
                            <td>${product.sellingPrice.toFixed(2)}</td>
                            <td>${product.quantity}</td>
                            <td>${product.totalCost.toFixed(2)}</td>
                            <td>${product.totalSelling.toFixed(2)}</td>
                            <td>${product.profit.toFixed(2)}</td>
                            <td>
                                <button class="btn btn-sm btn-danger btn-delete" data-id="${product.id}">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </td>
                        `;
                        productItems.appendChild(row);
                    });
                    
                    // إضافة مستمعي الأحداث لأزرار الحذف
                    document.querySelectorAll('.btn-delete').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const id = parseInt(this.getAttribute('data-id'));
                            deleteProduct(id);
                        });
                    });
                }
            }
            
            // تحديث الإجماليات
            function updateTotals() {
                let totalCostValue = 0;
                let totalSellingValue = 0;
                let totalProfitValue = 0;
                
                products.forEach(product => {
                    totalCostValue += product.totalCost;
                    totalSellingValue += product.totalSelling;
                    totalProfitValue += product.profit;
                });
                
                totalCost.textContent = totalCostValue.toFixed(2);
                totalSelling.textContent = totalSellingValue.toFixed(2);
                totalProfit.textContent = totalProfitValue.toFixed(2);
            }
            
            // حفظ المنتجات في التخزين المحلي
            function saveProducts() {
                localStorage.setItem('salesOrderProducts', JSON.stringify(products));
            }
            
            // التركيز على حقل الباركود عند تحميل الصفحة
            window.onload = function() {
                document.getElementById('barcode').focus();
            };
        });
    </script>
</body>
</html>