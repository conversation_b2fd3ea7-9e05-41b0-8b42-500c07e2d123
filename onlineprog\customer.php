<?php
session_start();

// التحقق من وجود سلة التسوق في الجلسة
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

// تحميل إعدادات الموقع
$settingsFile = 'site_settings.json';
$siteSettings = [];

// التحقق من وجود ملف الإعدادات
if (file_exists($settingsFile)) {
    $siteSettings = json_decode(file_get_contents($settingsFile), true);
} else {
    // إعدادات افتراضية
    $siteSettings = [
        'slider' => [
            'autoplay' => true,
            'delay' => 3000,
            'loop' => true,
            'effect' => 'slide',
            'show_navigation' => true,
            'show_pagination' => true,
            'items_count' => 6,
            'show_content' => false
        ],
        'site' => [
            'title' => 'متجر القرطاسية - الصفحة الرئيسية',
            'footer_text' => 'جميع الحقوق محفوظة - متجر القرطاسية',
            'logo' => 'default-logo.png'
        ],
        'categories' => [
            'icon_shape' => 'rounded',
            'border_radius' => 15,
            'hover_effect' => true
        ],
        'news_ticker' => [
            'enabled' => true,
            'speed' => 30,
            'direction' => 'right',
            'background_color' => '#3498db',
            'text_color' => '#ffffff'
        ],
        // إضافة الإعداد الجديد لإخفاء إعدادات السلايدر المخصص
        'hide_products_slider_settings' => false
    ];
}

// تحميل بيانات المنتجات
$productsData = [];
$dataFile = 'products_data.json';
if (file_exists($dataFile)) {
    $productsData = json_decode(file_get_contents($dataFile), true);
}

// تحميل بيانات الأقسام
$categoriesFile = 'categories_data.json';
$categoriesData = [];
if (file_exists($categoriesFile)) {
    $categoriesData = json_decode(file_get_contents($categoriesFile), true);
}

// تحميل بيانات الأخبار
$newsFile = 'news_data.json';
$newsData = [];

// التحقق من وجود ملف الأخبار
if (file_exists($newsFile)) {
    $newsData = json_decode(file_get_contents($newsFile), true);
    // تصفية الأخبار النشطة فقط
    $activeNews = [];
    foreach ($newsData as $news) {
        if (isset($news['active']) && $news['active']) {
            $activeNews[] = $news;
        }
    }
    $newsData = $activeNews;
}

// تحضير منتجات السلايدر (أحدث المنتجات)
$sliderProducts = [];
$maxSliderItems = isset($siteSettings['slider']['items_count']) ? $siteSettings['slider']['items_count'] : 6;

// ترتيب المنتجات بشكل عشوائي وأخذ العدد المطلوب للسلايدر
if (!empty($productsData)) {
    shuffle($productsData);
    $sliderProducts = array_slice($productsData, 0, $maxSliderItems);
}

// تصنيف المنتجات حسب الفئة
$categories = [];
$categorizedProducts = [];
$categoryData = []; // مصفوفة جديدة لتخزين بيانات الأقسام

foreach ($productsData as $index => $product) {
    $category = isset($product['category']) ? $product['category'] : 'أخرى';
    
    if (!in_array($category, $categories)) {
        $categories[] = $category;
        $categorizedProducts[$category] = [];
    }
    
    $product['id'] = $index;
    $categorizedProducts[$category][] = $product;
}

// إنشاء مصفوفة تربط بين اسم القسم وبياناته (بما في ذلك الصورة)
$categoryData = [];
foreach ($categoriesData as $category) {
    if (isset($category['name']) && isset($category['image']) && isset($category['active']) && $category['active']) {
        $categoryData[$category['name']] = $category;
    }
}
$categoryImages = [];
foreach ($categoriesData as $category) {
    if (isset($category['name']) && isset($category['image']) && isset($category['active']) && $category['active']) {
        $categoryImages[$category['name']] = $category['image'];
    }
}

// 1. إضافة إعداد جديد لتحديد قسم الإعلانات
if (!isset($siteSettings['ads_slider_category'])) {
    $siteSettings['ads_slider_category'] = 'إعلانات'; // اسم القسم الافتراضي للإعلانات
}

// 2. استخراج صور الإعلانات من القسم المحدد
$adsSliderCategory = $siteSettings['ads_slider_category'];
$adsSliderImages = [];
if (isset($categorizedProducts[$adsSliderCategory])) {
    foreach ($categorizedProducts[$adsSliderCategory] as $adProduct) {
        if (!empty($adProduct['image'])) {
            $adsSliderImages[] = [
                'image' => $adProduct['image'],
                'name' => $adProduct['name'] ?? '',
                'desc' => $adProduct['description'] ?? ''
            ];
        }
    }
}

// إضافة إعداد جديد لتحديد قسم السلايدر الخاص بالمنتجات
if (!isset($siteSettings['products_slider_category'])) {
    $siteSettings['products_slider_category'] = 'العاب'; // اسم القسم الافتراضي للسلايدر أصبح "العاب"
}

// استخراج صور المنتجات من القسم المحدد
$productsSliderCategory = $siteSettings['products_slider_category'];
$productsSliderImages = [];
if (isset($categorizedProducts[$productsSliderCategory])) {
    foreach ($categorizedProducts[$productsSliderCategory] as $prod) {
        if (!empty($prod['image'])) {
            $productsSliderImages[] = [
                'image' => $prod['image'],
                'name' => $prod['name'] ?? '',
                'desc' => $prod['description'] ?? ''
            ];
        }
    }
}

// تمرير متغيرات JavaScript
$jsVars = [
    'swiperEffect' => isset($siteSettings['slider']['effect']) ? $siteSettings['slider']['effect'] : 'slide',
    'swiperLoop' => isset($siteSettings['slider']['loop']) && $siteSettings['slider']['loop'] ? 'true' : 'false',
    'swiperAutoplay' => isset($siteSettings['slider']['autoplay']) && $siteSettings['slider']['autoplay'] ? 
        '{delay: ' . (isset($siteSettings['slider']['delay']) ? $siteSettings['slider']['delay'] : 3000) . ', disableOnInteraction: false}' : 'false'
];
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($siteSettings['site']['title']) ? $siteSettings['site']['title'] : 'متجر القرطاسية'; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- استدعاء ملف CSS الخارجي -->
    <link rel="stylesheet" href="styles.css">
    
    <!-- تعديل أنماط CSS الديناميكية -->
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
    
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
    
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 0;
            margin: 0;
        }
    
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem 0;
            text-align: center;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.8rem 1rem;
        }
    
        .header h1 {
            font-size: 2.2rem;
            margin: 0;
            font-weight: 600;
        }
    
        /* تعديل حجم العنوان للشاشات الصغيرة */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 1.8rem;
            }
        }
    
        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.5rem;
            }
        }
    
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px 50px;
        }
    
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            margin-bottom: 20px;
        }
    
        /* تعديل شريط التنقل للشاشات الصغيرة */
        @media (max-width: 768px) {
            .navbar {
                flex-direction: column;
                gap: 10px;
            }
        }
    
        .nav-links {
            display: flex;
            gap: 15px;
        }
    
        /* تعديل روابط التنقل للشاشات الصغيرة */
        @media (max-width: 480px) {
            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }
        }
    
        .nav-link {
            text-decoration: none;
            color: var(--dark-color);
            font-weight: 500;
            padding: 8px 12px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }
    
        .nav-link:hover {
            background-color: var(--light-color);
        }
    
        .nav-link.active {
            background-color: var(--secondary-color);
            color: white;
        }
    
        /* شريط الأخبار المتحرك */
        .news-ticker {
            background-color: <?php echo isset($siteSettings['news_ticker']['background_color']) ? $siteSettings['news_ticker']['background_color'] : '#3498db'; ?>;
            color: <?php echo isset($siteSettings['news_ticker']['text_color']) ? $siteSettings['news_ticker']['text_color'] : '#ffffff'; ?>;
            padding: 10px 0;
            overflow: hidden;
            margin-bottom: 20px;
            border-radius: var(--border-radius);
        }
    
        .news-ticker-content {
            display: flex;
            animation: ticker <?php echo isset($siteSettings['news_ticker']['speed']) ? $siteSettings['news_ticker']['speed'] : 30; ?>s linear infinite;
            animation-direction: <?php echo isset($siteSettings['news_ticker']['direction']) && $siteSettings['news_ticker']['direction'] === 'left' ? 'normal' : 'reverse'; ?>;
        }
    
        .news-item {
            white-space: nowrap;
            padding: 0 20px;
            font-weight: 500;
        }
    
        .news-separator {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 10px;
            color: rgba(255, 255, 255, 0.8);
        }
    
        .news-separator i {
            font-size: 0.8rem;
        }
    
        @keyframes ticker {
            0% {
                transform: translateX(100%);
            }
            100% {
                transform: translateX(-100%);
            }
        }
    
        /* السلايدر الرئيسي */
        .main-slider {
            margin-bottom: 30px;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
        }
    
        .swiper {
            width: 100%;
            height: 600px; /* تم زيادة الارتفاع من 300 إلى 400 للشاشات الكبيرة */
        }
    
        @media (max-width: 768px) {
            .swiper {
                height: 250px;
            }
        }
    
        @media (max-width: 480px) {
            .swiper {
                height: 200px;
            }
        }
    
        .swiper-slide {
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #fff;
            position: relative;
        }
    
        .swiper-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    
        .slide-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 15px;
            text-align: right;
        }
    
        .slide-content h3 {
            margin: 0 0 5px 0;
            font-size: 1.5rem;
        }
    
        .slide-content p {
            margin: 0;
            font-size: 1rem;
        }
    
        /* أقسام المنتجات */
        .categories-section {
            margin-bottom: 30px;
        }
    
        .section-title {
            font-size: 1.8rem;
            margin-bottom: 20px;
            color: var(--dark-color);
            position: relative;
            padding-bottom: 10px;
        }
    
        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background-color: var(--secondary-color);
        }
    
        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 20px;
        }
    
        /* إضافة تخصيص للشاشات الصغيرة */
        @media (max-width: 768px) {
            .categories-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }
    
        @media (max-width: 576px) {
            .categories-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 15px;
            }
        }
    
        .category-item {
            text-align: center;
            text-decoration: none;
            color: var(--dark-color);
            transition: var(--transition);
        }
    
        .category-item:hover {
            transform: translateY(-5px);
        }
    
        .category-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--light-color);
            border-radius: <?php echo isset($siteSettings['categories']['border_radius']) ? $siteSettings['categories']['border_radius'] . 'px' : '15px'; ?>;
            font-size: 2rem;
            color: var(--secondary-color);
            transition: var(--transition);
            overflow: hidden;
        }
    
        .category-icon img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    
        .category-item:hover .category-icon {
            background-color: var(--secondary-color);
        }
    
        .category-name {
            font-weight: 500;
        }
    
    
    
        @media (max-width: 768px) {
            .mobile-nav {
                display: flex;
                justify-content: space-around;
                align-items: center;
            }
            
            .container {
                padding-bottom: 80px;
            }
            
            .footer {
                margin-bottom: 60px;
            }
        }
    
        
        
    
        
        
        /* أنماط الهيدر الجديد */
        .header-menu-icon {
            font-size: 1.5rem;
            color: white;
            cursor: pointer;
            display: none;
        }
        
        .header-logo {
            text-align: center;
        }
        
        .header-logo img {
            height: 40px;
            max-width: 150px;
            object-fit: contain;
        }
        
        .header-user {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            background-color: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .header-user img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .header-user i {
            font-size: 1.5rem;
            color: white;
        }
        
        /* قائمة الهيدر المنسدلة */
        .header-menu {
            position: fixed;
            top: 0;
            right: -280px;
            width: 280px;
            height: 100%;
            background-color: white;
            z-index: 1002;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
            transition: right 0.3s ease;
            padding: 20px;
            overflow-y: auto;
        }
        
        .header-menu.active {
            right: 0;
        }
        
        .header-menu-close {
            position: absolute;
            top: 15px;
            left: 15px;
            font-size: 1.2rem;
            color: #333;
            cursor: pointer;
            background: none;
            border: none;
        }
        
        .header-menu-items {
            margin-top: 50px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .header-menu-item {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: #333;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .header-menu-item i {
            margin-left: 10px;
            font-size: 1.2rem;
            width: 24px;
            text-align: center;
        }
        
        /* تعديلات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .header-menu-icon {
                display: block;
            }
            
            .navbar {
                display: none;
            }
            
            .header h1 {
                display: none;
            }
        }
        
        /* خلفية معتمة عند فتح القائمة */
        .menu-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1001;
            display: none;
        }
        
        .menu-overlay.active {
            display: block;
        }
        
        /* أنماط مربع البحث الاحترافي */
        .search-box-container {
            margin-bottom: 20px;
        }
        
        .search-form {
            display: flex;
            align-items: center;
        }
        
        .search-input-container {
            flex: 1;
            position: relative;
        }
        
        .search-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid <?php echo isset($siteSettings['search_box']['border_color']) ? $siteSettings['search_box']['border_color'] : '#337fd1'; ?>;
            border-radius: var(--border-radius);
            font-family: 'Cairo', sans-serif;
            font-size: 1rem;
            background-color: <?php echo isset($siteSettings['search_box']['background_color']) ? $siteSettings['search_box']['background_color'] : '#f8f9fa'; ?>;
            color: <?php echo isset($siteSettings['search_box']['text_color']) ? $siteSettings['search_box']['text_color'] : '#333333'; ?>;
            transition: var(--transition);
        }
        
        .search-input:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(51, 127, 209, 0.2);
        }
        
        .search-button {
            background-color: <?php echo isset($siteSettings['search_box']['button_color']) ? $siteSettings['search_box']['button_color'] : '#337fd1'; ?>;
            color: <?php echo isset($siteSettings['search_box']['button_text_color']) ? $siteSettings['search_box']['button_text_color'] : '#ffffff'; ?>;
            border: none;
            border-radius: var(--border-radius);
            padding: 12px 20px;
            margin-right: 10px;
            cursor: pointer;
            font-size: 1rem;
            transition: var(--transition);
        }
        
        .search-button:hover {
            opacity: 0.9;
        }
        
        .animated-placeholder {
            position: absolute;
            top: 50%;
            right: 15px;
            transform: translateY(-50%);
            color: #999;
            pointer-events: none;
            transition: var(--transition);
        }
        
        .search-input:focus + .animated-placeholder {
            display: none;
        }
        
        @media (max-width: 768px) {
            .search-box-container {
                margin-bottom: 15px;
            }
            
            .search-input {
                padding: 10px 12px;
                font-size: 0.9rem;
            }
            
            .search-button {
                padding: 10px 15px;
                font-size: 0.9rem;
            }
        }

        /* تصميم قسم المنتجات */
    .products-section {
        margin-bottom: 30px;
    }

    .products-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        margin-bottom: 30px;
    }

    .product-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        padding: 15px;
        position: relative;
        display: flex;
        flex-direction: column;
    }

    .product-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-radius: 8px;
        margin-bottom: 15px;
    }

    .product-title {
        font-size: 1.2rem;
        margin-bottom: 10px;
        font-weight: 600;
    }

    .product-category {
        color: #666;
        margin-bottom: 10px;
        font-size: 0.9rem;
    }

    .product-price {
        font-size: 1.3rem;
        color: #2ecc71;
        margin-bottom: 15px;
        font-weight: 600;
    }

    .add-to-cart {
        background: #3498db;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 5px;
        cursor: pointer;
        transition: 0.3s;
        margin-right: auto;
    }

    .add-to-cart:hover {
        background: #2980b9;
    }

    /* تعديلات للشاشات الصغيرة */
    @media (max-width: 768px) {
        .products-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .product-card {
            padding: 10px;
        }

        .product-image {
            height: 150px;
        }

        .product-title {
            font-size: 1rem;
        }

        .product-price {
            font-size: 1.1rem;
        }

        .add-to-cart {
            padding: 6px 12px;
            font-size: 0.9rem;
        }
    }

    @media (max-width: 480px) {
        .product-image {
            height: 120px;
        }

        .product-title {
            font-size: 0.9rem;
        }

        .product-category {
            font-size: 0.8rem;
        }

        .product-price {
            font-size: 1rem;
        }
    }

    /* سلايدر الإعلانات */
    .ads-slider {
        margin-bottom: 30px;
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--box-shadow);
        background: #fff;
    }
    .ads-swiper {
        width: 100%;
        height: 220px;
    }
    .ads-swiper .swiper-slide {
        display: flex;
        justify-content: center;
        align-items: center;
        background: #fff;
        position: relative;
        width: 100%;
        height: 220px;
    }
    .ads-swiper .swiper-slide img {
        width: 100%;
        height: 100%;
        object-fit: cover; /* عرض الصورة بالحجم الكامل وتعبئة السلايد */
        border-radius: 0;
    }
    .ads-slide-content {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0,0,0,0.3);
        color: #fff;
        padding: 10px;
        text-align: right;
    }

    /* سلايدر المنتجات من قسم معين */
        .products-slider {
            margin-bottom: 30px;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
            background: #fff;
        }
        .products-swiper {
            width: 100%;
            height: 220px;
        }
        .products-swiper .swiper-slide {
            display: flex;
            justify-content: center;
            align-items: center;
            background: #fff;
            position: relative;
            width: 100%;
            height: 220px;
        }
        .products-swiper .swiper-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover; /* عرض الصورة بالحجم الكامل وتعبئة السلايد */
            border-radius: 0;
        }
        .products-slide-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0,0,0,0.3);
            color: #fff;
            padding: 10px;
            text-align: right;
        }
    </style>
    
    <!-- تمرير متغيرات JavaScript -->
    <script>
        // تعريف المتغيرات التي سيتم استخدامها في ملف JavaScript
        var swiperEffect = '<?php echo $jsVars['swiperEffect']; ?>';
        var swiperLoop = <?php echo $jsVars['swiperLoop']; ?>;
        var swiperAutoplay = <?php echo $jsVars['swiperAutoplay']; ?>;
    </script>
</head>
<body>
    <div class="header">
        <div class="header-menu-icon" id="header-menu-toggle">
            <i class="fas fa-bars"></i>
        </div>
        
        <div class="header-logo">
            <?php if (isset($siteSettings['site']['logo']) && !empty($siteSettings['site']['logo'])): ?>
                <img src="uploads/<?php echo $siteSettings['site']['logo']; ?>" alt="شعار الموقع">
            <?php else: ?>
                <h1><?php echo isset($siteSettings['site']['title']) ? $siteSettings['site']['title'] : 'متجر القرطاسية'; ?></h1>
            <?php endif; ?>
        </div>
        
        <div class="header-user">
            <?php if (isset($_SESSION['customer_id']) && isset($customerData['profile_image'])): ?>
                <img src="uploads/<?php echo $customerData['profile_image']; ?>" alt="صورة المستخدم">
            <?php else: ?>
                <i class="fas fa-user"></i>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- قائمة الهيدر المنسدلة -->
    <div class="header-menu" id="header-menu">
        <button class="header-menu-close" id="header-menu-close">
            <i class="fas fa-times"></i>
        </button>
        
        <div class="header-menu-items">
            <a href="customer.php" class="header-menu-item">
                <i class="fas fa-home"></i>
                <span>الرئيسية</span>
            </a>
            <a href="customer_products.php" class="header-menu-item">
                <i class="fas fa-shopping-bag"></i>
                <span>المنتجات</span>
            </a>
            <a href="customer_cart.php" class="header-menu-item">
                <i class="fas fa-shopping-cart"></i>
                <span>سلة التسوق (<?php echo count($_SESSION['cart']); ?>)</span>
            </a>
            <a href="customer_orders.php" class="header-menu-item">
                <i class="fas fa-box"></i>
                <span>طلباتي</span>
            </a>
            
            <?php if (isset($_SESSION['customer_id'])): ?>
                <a href="customer_profile.php" class="header-menu-item">
                    <i class="fas fa-user-cog"></i>
                    <span>الملف الشخصي</span>
                </a>
                <a href="customer_logout.php" class="header-menu-item">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </a>
            <?php else: ?>
                <a href="customer_login.php" class="header-menu-item">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>تسجيل الدخول</span>
                </a>
                <a href="customer_register.php" class="header-menu-item">
                    <i class="fas fa-user-plus"></i>
                    <span>إنشاء حساب</span>
                </a>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- خلفية معتمة عند فتح القائمة -->
    <div class="menu-overlay" id="menu-overlay"></div>
    
    <div class="container">
        <div class="navbar">
            <div class="nav-links">
                <a href="customer.php" class="nav-link active">الرئيسية</a>
                <a href="customer_products.php" class="nav-link">المنتجات</a>
                <a href="customer_cart.php" class="nav-link">سلة التسوق (<?php echo count($_SESSION['cart']); ?>)</a>
                <a href="customer_orders.php" class="nav-link">طلباتي</a>
            </div>
            
            <div class="user-actions">
                <?php if (isset($_SESSION['customer_id'])): ?>
                    <a href="customer_logout.php" class="nav-link">تسجيل الخروج</a>
                <?php else: ?>
                    <a href="customer_login.php" class="nav-link">تسجيل الدخول</a>
                    <a href="customer_register.php" class="nav-link">إنشاء حساب</a>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- مربع البحث الاحترافي -->
        <?php if (isset($siteSettings['search_box']['enabled']) && $siteSettings['search_box']['enabled']): ?>
        <div class="search-box-container">
            <form action="customer_products.php" method="get" class="search-form">
                <div class="search-input-container">
                    <input type="text" name="search" id="search-input" class="search-input" 
                           placeholder="<?php echo isset($siteSettings['search_box']['placeholder_text']) ? $siteSettings['search_box']['placeholder_text'] : 'ابحث عن منتجات...'; ?>">
                    <?php if (isset($siteSettings['search_box']['animation_enabled']) && $siteSettings['search_box']['animation_enabled']): ?>
                    <div class="animated-placeholder" id="animated-placeholder"></div>
                    <?php endif; ?>
                </div>
                <button type="submit" class="search-button">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
        <?php endif; ?>
        
        <?php if (isset($siteSettings['news_ticker']['enabled']) && $siteSettings['news_ticker']['enabled'] && !empty($newsData)): ?>
        <div class="news-ticker">
            <div class="news-ticker-content">
                <?php foreach ($newsData as $news): ?>
                    <div class="news-item"><?php echo $news['title']; ?></div>
                    <div class="news-separator"><i class="fas fa-newspaper"></i></div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="main-slider">
            <div class="swiper">
                <div class="swiper-wrapper">
                    <?php if (!empty($sliderProducts)): ?>
                        <?php foreach ($sliderProducts as $product): ?>
                            <div class="swiper-slide">
                                <?php 
                                $imagePath = !empty($product['image']) ? 'uploads/' . $product['image'] : 'https://via.placeholder.com/600x300';
                                ?>
                                <img src="<?php echo $imagePath; ?>" alt="<?php echo $product['name']; ?>">
                                <?php if (isset($siteSettings['slider']['show_content']) && $siteSettings['slider']['show_content']): ?>
                                <div class="slide-content">
                                    <h3><?php echo $product['name']; ?></h3>
                                    <p><?php echo number_format($product['price'], 2); ?> ريال</p>
                                </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="swiper-slide">
                            <img src="https://via.placeholder.com/600x300" alt="صورة افتراضية">
                            <div class="slide-content">
                                <h3>مرحباً بك في متجر القرطاسية</h3>
                                <p>تسوق الآن واحصل على أفضل العروض</p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                
                <?php if (isset($siteSettings['slider']['show_pagination']) && $siteSettings['slider']['show_pagination']): ?>
                <div class="swiper-pagination"></div>
                <?php endif; ?>
                
                <?php if (isset($siteSettings['slider']['show_navigation']) && $siteSettings['slider']['show_navigation']): ?>
                <div class="swiper-button-next"></div>
                <div class="swiper-button-prev"></div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- 3. سلايدر الإعلانات -->
        <?php if (!empty($adsSliderImages)): ?>
        <div class="ads-slider">
            <div class="swiper ads-swiper">
                <div class="swiper-wrapper">
                    <?php foreach ($adsSliderImages as $ad): ?>
                    <div class="swiper-slide" style="position:relative;">
                        <img src="uploads/<?php echo $ad['image']; ?>" alt="<?php echo htmlspecialchars($ad['name']); ?>">
                        <?php if (!empty($ad['name']) || !empty($ad['desc'])): ?>
                        <div class="ads-slide-content">
                            <?php if (!empty($ad['name'])): ?>
                                <div style="font-weight:bold;"><?php echo htmlspecialchars($ad['name']); ?></div>
                            <?php endif; ?>
                            <?php if (!empty($ad['desc'])): ?>
                                <div style="font-size:0.95em;"><?php echo htmlspecialchars($ad['desc']); ?></div>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
                <div class="swiper-pagination"></div>
                <div class="swiper-button-next"></div>
                <div class="swiper-button-prev"></div>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- سلايدر المنتجات من قسم معين -->
        <?php if (!$siteSettings['hide_products_slider_settings'] && !empty($productsSliderImages)): ?>
        <div class="products-slider">
            <div class="swiper products-swiper">
                <div class="swiper-wrapper">
                    <?php foreach ($productsSliderImages as $prod): ?>
                    <div class="swiper-slide">
                        <img src="uploads/<?php echo $prod['image']; ?>" alt="<?php echo htmlspecialchars($prod['name']); ?>">
                        <?php if (!empty($prod['name']) || !empty($prod['desc'])): ?>
                        <div class="products-slide-content">
                            <?php if (!empty($prod['name'])): ?>
                                <div style="font-weight:bold;"><?php echo htmlspecialchars($prod['name']); ?></div>
                            <?php endif; ?>
                            <?php if (!empty($prod['desc'])): ?>
                                <div style="font-size:0.95em;"><?php echo htmlspecialchars($prod['desc']); ?></div>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
                <div class="swiper-pagination"></div>
                <div class="swiper-button-next"></div>
                <div class="swiper-button-prev"></div>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="categories-section">
            <div class="categories-grid">
                <?php foreach ($categories as $category): ?>
                    <a href="customer_products.php?category=<?php echo urlencode($category); ?>" class="category-item">
                        <div class="category-icon">
                            <?php if (isset($categoryData[$category]) && isset($categoryData[$category]['image'])): ?>
                                <img src="uploads/<?php echo $categoryData[$category]['image']; ?>" alt="<?php echo $category; ?>">
                            <?php else: ?>
                                <i class="fas fa-box"></i>
                            <?php endif; ?>
                        </div>
                        <div class="category-name"><?php echo $category; ?></div>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>

        <?php
        // عرض بطاقات المنتجات لقسم شناتي
        foreach ($categories as $category):
            if (mb_strtolower($category) !== 'شناتي') continue;
        ?>
        <div class="products-section">
            <div class="products-grid">
                <?php 
                $categoryProducts = array_slice($categorizedProducts[$category], 0, 2);
                foreach ($categoryProducts as $product): 
                ?>
                    <div class="product-card">
                        <img src="uploads/<?php echo isset($product['image']) ? $product['image'] : 'default-product.jpg'; ?>" alt="<?php echo $product['name']; ?>" class="product-image" style="height:160px;width:160px;object-fit:contain;display:block;margin:0 auto 15px;">
                        <h3 class="product-title"><?php echo $product['name']; ?></h3>
                        <div class="product-price"><?php echo $product['price']; ?> د.ك</div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endforeach; ?>

        <?php
        // عرض بطاقات المنتجات فقط لقسم tornado
        foreach ($categories as $category):
            if (mb_strtolower($category) !== 'tornado') continue;
        ?>
        <div class="products-section">
            <!-- تم إخفاء إطار واسم القسم -->
            <div class="products-grid">
                <?php 
                $categoryProducts = array_slice($categorizedProducts[$category], 0, 2);
                foreach ($categoryProducts as $product): 
                ?>
                    <div class="product-card">
                        <img src="uploads/<?php echo isset($product['image']) ? $product['image'] : 'default-product.jpg'; ?>" alt="<?php echo $product['name']; ?>" class="product-image" style="height:160px;width:160px;object-fit:contain;display:block;margin:0 auto 15px;">
                        <h3 class="product-title"><?php echo $product['name']; ?></h3>
                        <div class="product-price"><?php echo $product['price']; ?> د.ك</div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endforeach; ?>

        <!-- قسم اللعاب التحدي -->
        <div class="products-section">
            <!-- تم إخفاء إطار واسم القسم -->
            <div class="products-grid">
                <?php
                $challengeCategory = null;
                foreach ($categories as $cat) {
                    if (mb_strtolower($cat) === 'العاب التحدي' || mb_strtolower($cat) === 'العاب التحدى') {
                        $challengeCategory = $cat;
                        break;
                    }
                }
                if ($challengeCategory && isset($categorizedProducts[$challengeCategory])) {
                    $challengeProducts = array_slice($categorizedProducts[$challengeCategory], 0, 2);
                    foreach ($challengeProducts as $product):
                ?>
                    <div class="product-card">
                        <img src="uploads/<?php echo isset($product['image']) ? $product['image'] : 'default-product.jpg'; ?>" alt="<?php echo $product['name']; ?>" class="product-image" style="height:160px;width:160px;object-fit:contain;display:block;margin:0 auto 15px;">
                        <h3 class="product-title"><?php echo $product['name']; ?></h3>
                        <div class="product-price"><?php echo $product['price']; ?> د.ك</div>
                    </div>
                <?php
                    endforeach;
                } else {
                    echo '<div style="padding:20px;color:#888;">لا توجد منتجات في قسم العاب التحدي حالياً.</div>';
                }
                ?>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p><?php echo isset($siteSettings['site']['footer_text']) ? $siteSettings['site']['footer_text'] : 'جميع الحقوق محفوظة - متجر القرطاسية'; ?> © <?php echo date('Y'); ?></p>
    </div>
    
   
    
    
    <!-- استدعاء مكتبة Swiper.js -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
    <script src="scripts.js"></script>
    
    <!-- سكريبت لتهيئة السلايدر -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة السلايدر
            var swiper = new Swiper('.swiper', {
                effect: swiperEffect,
                loop: swiperLoop,
                autoplay: swiperAutoplay,
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
            });
            
            // 5. تهيئة سلايدر الإعلانات
            <?php if (!empty($adsSliderImages)): ?>
            new Swiper('.ads-swiper', {
                loop: true,
                autoplay: { delay: 3500, disableOnInteraction: false },
                pagination: { el: '.ads-swiper .swiper-pagination', clickable: true },
                navigation: {
                    nextEl: '.ads-swiper .swiper-button-next',
                    prevEl: '.ads-swiper .swiper-button-prev',
                },
            });
            <?php endif; ?>

            // تهيئة سلايدر المنتجات من قسم معين
            <?php if (!empty($productsSliderImages)): ?>
            new Swiper('.products-swiper', {
                loop: true,
                autoplay: { delay: 3500, disableOnInteraction: false },
                pagination: { el: '.products-swiper .swiper-pagination', clickable: true },
                navigation: {
                    nextEl: '.products-swiper .swiper-button-next',
                    prevEl: '.products-swiper .swiper-button-prev',
                },
            });
            <?php endif; ?>

            
            // تفعيل قائمة الهيدر المنسدلة
            const menuToggle = document.getElementById('header-menu-toggle');
            const headerMenu = document.getElementById('header-menu');
            const menuClose = document.getElementById('header-menu-close');
            const menuOverlay = document.getElementById('menu-overlay');
            
            menuToggle.addEventListener('click', function() {
                headerMenu.classList.add('active');
                menuOverlay.classList.add('active');
                document.body.style.overflow = 'hidden';
            });
            
            menuClose.addEventListener('click', function() {
                headerMenu.classList.remove('active');
                menuOverlay.classList.remove('active');
                document.body.style.overflow = '';
            });
            
            menuOverlay.addEventListener('click', function() {
                headerMenu.classList.remove('active');
                menuOverlay.classList.remove('active');
                document.body.style.overflow = '';
            });
            
            // سكريبت للنص المتحرك في مربع البحث
            const searchInput = document.getElementById('search-input');
            const animatedPlaceholder = document.getElementById('animated-placeholder');
            
            if (searchInput && animatedPlaceholder) {
                const placeholderTexts = [
                    "ابحث عن دفاتر...",
                    "ابحث عن أقلام...",
                    "ابحث عن حقائب...",
                    "ابحث عن ألوان...",
                    "ابحث عن أدوات هندسية..."
                ];
                
                let currentIndex = 0;
                
                function updatePlaceholder() {
                    animatedPlaceholder.textContent = placeholderTexts[currentIndex];
                    currentIndex = (currentIndex + 1) % placeholderTexts.length;
                }
                
                // تحديث النص المتحرك كل 3 ثواني
                updatePlaceholder();
                setInterval(updatePlaceholder, <?php echo isset($siteSettings['search_box']['animation_speed']) ? $siteSettings['search_box']['animation_speed'] * 1000 : 3000; ?>);
                
                // إخفاء النص المتحرك عند التركيز على حقل البحث
                searchInput.addEventListener('focus', function() {
                    animatedPlaceholder.style.display = 'none';
                });
                
                // إظهار النص المتحرك عند إلغاء التركيز إذا كان حقل البحث فارغًا
                searchInput.addEventListener('blur', function() {
                    if (searchInput.value === '') {
                        animatedPlaceholder.style.display = 'block';
                    }
                });
            }
        });
    function addToCart(productId) {
        fetch('add_to_cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                product_id: productId
            })
        })
        .then(response => response.json())
        .then data => {
            if (data.success) {
                const cartCount = document.querySelector('.cart-count');
                if (cartCount) {
                    cartCount.textContent = data.cart_count;
                }
                alert('تمت إضافة المنتج إلى السلة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إضافة المنتج إلى السلة');
        });
    }
    </script>

<!-- 🚩 Bottom Navigation Bar Start -->
<style>
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70px;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -1px 10px rgba(0,0,0,0.1);
  padding: 0 10px;
  z-index: 999;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #aaa;
  font-size: 12px;
  text-decoration: none;
}

.nav-item .icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.nav-item.active {
  color: #3b5bdb;
}

.nav-center-btn {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(0,0,0,0.15);
  display: flex;
  align-items: center;
  justify-content: center;
}

.center-button {
  background-color: #6c4ce3;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-size: 24px;
}
</style>

<div class="bottom-nav">
  <a href="customer.php" class="nav-item active">
    <i class="icon">🏠</i>
    <span>Home</span>
  </a>
  <a href="#" class="nav-item">
    <i class="icon">🔍</i>
    <span>Search</span>
  </a>

  <div class="nav-center-btn">
    <a href="#" class="center-button">
      <i class="icon">🏬</i>
    </a>
  </div>

  <a href="customer_cart.php" class="nav-item">
    <i class="icon">🛒</i>
    <span>Cart</span>
  </a>
  <a href="#" class="nav-item">
    <i class="icon">👤</i>
    <span>Profile</span>
  </a>
</div>
<!-- 🚩 Bottom Navigation Bar End -->

</body>
</html>
