<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء عروض الأسعار - نظام إدارة المنتجات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- إضافة مكتبة JsBarcode لإنشاء الباركود -->
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 0;
            margin: 0;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem 0;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.2rem;
            margin: 0;
            font-weight: 600;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px 50px;
        }
        
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .card-header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem 1.5rem;
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .card-header i {
            margin-left: 10px;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1.2rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        input[type="text"], 
        input[type="number"], 
        input[type="file"],
        select {
            width: 100%;
            padding: 0.8rem 1rem;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: var(--transition);
        }
        
        input[type="text"]:focus, 
        input[type="number"]:focus,
        select:focus {
            border-color: var(--secondary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }
        
        .btn {
            display: inline-block;
            background-color: var(--secondary-color);
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            text-align: center;
            transition: var(--transition);
            text-decoration: none;
        }
        
        .btn:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background-color: var(--secondary-color);
        }
        
        .btn-success {
            background-color: var(--success-color);
        }
        
        .btn-danger {
            background-color: var(--danger-color);
        }
        
        .btn-warning {
            background-color: var(--warning-color);
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
        
        /* منطقة السحب والإفلات */
        #drop-area {
            border: 3px dashed var(--secondary-color);
            border-radius: var(--border-radius);
            padding: 2.5rem;
            text-align: center;
            background-color: rgba(52, 152, 219, 0.05);
            color: #666;
            cursor: pointer;
            transition: var(--transition);
            margin-bottom: 1.5rem;
            position: relative;
        }
        
        #drop-area.highlight {
            background-color: rgba(52, 152, 219, 0.1);
            border-color: #2980b9;
        }
        
        #drop-area p {
            font-size: 1.2rem;
            margin: 0;
            color: #666;
        }
        
        #drop-area .icon {
            font-size: 3rem;
            color: var(--secondary-color);
            margin-bottom: 1rem;
            display: block;
        }
        
        .preview-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }
        
        .image-preview {
            background-color: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
            padding: 1rem;
            display: flex;
            flex-direction: column;
        }
        
        .image-preview img {
            width: 100%;
            height: 180px;
            object-fit: cover;
            border-radius: calc(var(--border-radius) - 4px);
            margin-bottom: 1rem;
        }
        
        .image-preview input,
        .image-preview select {
            margin-bottom: 0.8rem;
            font-size: 0.9rem;
        }
        
        .barcode-container {
            text-align: center;
            margin: 1rem 0;
        }
        
        .barcode-container svg {
            max-width: 100%;
            height: 60px;
        }
        
        .default-controls {
            background-color: #f8f9fa;
            border-radius: var(--border-radius);
            padding: 1rem;
            margin: 1.5rem 0;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 0.8rem;
            margin-bottom: 0.8rem;
        }
        
        .control-group:last-child {
            margin-bottom: 0;
        }
        
        .control-group label {
            margin-bottom: 0;
            white-space: nowrap;
            min-width: 120px;
        }
        
        .control-group input,
        .control-group select {
            flex: 1;
            min-width: 120px;
        }
        
        .color-container {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .color-item {
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }
        
        .remove-color {
            cursor: pointer;
            color: var(--danger-color);
            font-size: 0.8rem;
        }
        
        .add-color-btn {
            cursor: pointer;
            color: var(--secondary-color);
            font-size: 0.9rem;
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            text-align: center;
            padding: 1.5rem 0;
            margin-top: 3rem;
        }
        
        .back-link {
            display: inline-block;
            margin-bottom: 1rem;
            color: var(--secondary-color);
            text-decoration: none;
            font-weight: 600;
        }
        
        .back-link i {
            margin-left: 5px;
        }
        
        .back-link:hover {
            color: #2980b9;
        }
        
        /* أنماط للطباعة */
        @media print {
            .header-buttons, .footer, .back-link, .default-controls, #drop-area, form > .btn {
                display: none !important;
            }
            
            .header {
                background-color: white !important;
                color: black !important;
                padding: 10px 0 !important;
                margin-bottom: 20px !important;
                box-shadow: none !important;
            }
            
            body {
                background-color: white !important;
            }
            
            .card {
                box-shadow: none !important;
                margin-bottom: 10px !important;
            }
            
            .card-header {
                background-color: white !important;
                color: black !important;
                border-bottom: 1px solid #eee !important;
            }
            
            .preview-container {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)) !important;
            }
            
            .image-preview {
                box-shadow: none !important;
                border: 1px solid #eee !important;
            }
            
            /* تأكد من طباعة الألوان بشكل صحيح */
            .color-box {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                color-adjust: exact !important;
            }
            
            /* تحسين عرض الباركود للطباعة */
            .barcode-container svg {
                height: 50px !important;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>إنشاء عروض الأسعار</h1>
    </div>
    
    <div class="container">
        <a href="price_offers.php" class="back-link"><i class="fas fa-arrow-right"></i> العودة إلى قائمة عروض الأسعار</a>
        
        <div class="card">
            <div class="card-header">
                <span><i class="fas fa-tags"></i> إنشاء عروض الأسعار</span>
                <div class="header-buttons">
                    <button id="print-btn" class="btn btn-sm btn-success"><i class="fas fa-print"></i> طباعة العروض</button>
                </div>
            </div>
            <div class="card-body">
                <form action="save_price_offers.php" method="post" enctype="multipart/form-data">
                    <div id="drop-area">
                        <i class="fas fa-cloud-upload-alt icon"></i>
                        <p>اسحب وأفلت الصور هنا أو انقر للاختيار</p>
                        <input type="file" id="fileElem" name="images[]" accept="image/*" multiple style="display:none" onchange="handleFiles(this.files)">
                    </div>
                    
                    <div class="default-controls">
                        <div class="control-group">
                            <label for="default_cost_price"><i class="fas fa-money-bill-wave"></i> سعر التكلفة الافتراضي:</label>
                            <input type="number" id="default_cost_price" name="default_cost_price" step="0.01" value="0">
                            <button type="button" class="btn btn-primary btn-sm" onclick="applyDefaultCostPrice()">
                                <i class="fas fa-check"></i> تطبيق على الجميع
                            </button>
                        </div>
                        
                        <div class="control-group">
                            <label for="default_selling_price"><i class="fas fa-money-bill-wave"></i> سعر البيع الافتراضي:</label>
                            <input type="number" id="default_selling_price" name="default_selling_price" step="0.01" value="0">
                            <button type="button" class="btn btn-primary btn-sm" onclick="applyDefaultSellingPrice()">
                                <i class="fas fa-check"></i> تطبيق على الجميع
                            </button>
                        </div>
                        
                        <div class="control-group">
                            <label for="default_quantity"><i class="fas fa-cubes"></i> الكمية الافتراضية:</label>
                            <input type="number" id="default_quantity" name="default_quantity" min="1" value="1">
                            <button type="button" class="btn btn-primary btn-sm" onclick="applyDefaultQuantity()">
                                <i class="fas fa-check"></i> تطبيق على الجميع
                            </button>
                        </div>
                        
                        <div class="control-group">
                            <label><i class="fas fa-palette"></i> الألوان الافتراضية:</label>
                            <div>
                                <div id="default-colors-container" class="color-container">
                                    <div class="color-item">
                                        <input type="color" value="#000000" onchange="updateDefaultColorsInput()">
                                        <span class="remove-color" onclick="removeDefaultColor(this)"><i class="fas fa-times"></i></span>
                                    </div>
                                    <span class="add-color-btn" onclick="addDefaultColor()"><i class="fas fa-plus"></i> إضافة لون</span>
                                </div>
                                <input type="hidden" id="default_colors" name="default_colors" value="#000000">
                                <button type="button" class="btn btn-primary btn-sm" onclick="applyDefaultColors()">
                                    <i class="fas fa-check"></i> تطبيق على الجميع
                                </button>
                            </div>
                        </div>
                        
                        <div class="control-group">
                            <label for="generate_barcodes"><i class="fas fa-barcode"></i> إنشاء باركود تلقائي:</label>
                            <div>
                                <button type="button" class="btn btn-primary btn-sm" onclick="generateAllBarcodes()">
                                    <i class="fas fa-barcode"></i> إنشاء باركود لجميع المنتجات
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div id="preview-container" class="preview-container"></div>
                    
                    <button type="submit" class="btn btn-success" id="upload-button" style="display:none">
                        <i class="fas fa-save"></i> حفظ عروض الأسعار
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>جميع الحقوق محفوظة &copy; <?php echo date('Y'); ?> نظام إدارة المنتجات</p>
    </div>

    <script>
        // إضافة وظائف السحب والإفلات
        const dropArea = document.getElementById('drop-area');
        const fileElem = document.getElementById('fileElem');
        const previewContainer = document.getElementById('preview-container');
        const uploadButton = document.getElementById('upload-button');
        
        // منع السلوك الافتراضي للمتصفح
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        // إضافة تأثيرات بصرية
        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, unhighlight, false);
        });
        
        function highlight() {
            dropArea.classList.add('highlight');
        }
        
        function unhighlight() {
            dropArea.classList.remove('highlight');
        }
        
        // معالجة الملفات المسقطة
        dropArea.addEventListener('drop', handleDrop, false);
        dropArea.addEventListener('click', () => fileElem.click(), false);
        
        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }
        
        // دالة لإنشاء باركود تلقائي
        function generateBarcode() {
            // إنشاء باركود من 13 رقم (مشابه لمعيار EAN-13)
            let barcode = '';
            // إضافة رقم البلد (مثلاً 6 للسعودية)
            barcode += '6';
            // إضافة 11 رقم عشوائي
            for (let i = 0; i < 11; i++) {
                barcode += Math.floor(Math.random() * 10);
            }
            // إضافة رقم التحقق (يمكن تحسينه لاحقاً لحساب رقم التحقق الفعلي)
            barcode += Math.floor(Math.random() * 10);
            return barcode;
        }
        
        // معالجة الملفات المختارة
        function handleFiles(files) {
            previewContainer.innerHTML = '';
            if (files.length > 0) {
                uploadButton.style.display = 'inline-block';
                Array.from(files).forEach((file, index) => {
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const barcode = generateBarcode();
                            const preview = document.createElement('div');
                            preview.className = 'image-preview';
                            preview.innerHTML = `
                                <img src="${e.target.result}" alt="Preview">
                                <input type="text" name="names[]" placeholder="اسم المنتج" required>
                                <div class="barcode-container">
                                    <svg id="barcode-${index}"></svg>
                                    <input type="text" name="barcodes[]" value="${barcode}" onchange="updateBarcode(this, ${index})">
                                </div>
                                <input type="number" name="cost_prices[]" placeholder="سعر التكلفة" step="0.01" required>
                                <input type="number" name="selling_prices[]" placeholder="سعر البيع" step="0.01" required>
                                <input type="number" name="quantities[]" placeholder="الكمية" min="1" value="1" required>
                                <div class="colors-section">
                                    <label><i class="fas fa-palette"></i> الألوان:</label>
                                    <div class="color-container">
                                        <div class="color-item">
                                            <input type="color" value="#000000" onchange="updateColorsInput(this)">
                                            <span class="remove-color" onclick="removeColor(this)"><i class="fas fa-times"></i></span>
                                        </div>
                                        <span class="add-color-btn" onclick="addColor(this)"><i class="fas fa-plus"></i> إضافة لون</span>
                                    </div>
                                    <input type="hidden" name="colors[]" value="#000000">
                                </div>
                            `;
                            previewContainer.appendChild(preview);
                            
                            // إنشاء الباركود
                            JsBarcode(`#barcode-${index}`, barcode, {
                                format: "EAN13",
                                lineColor: "#000",
                                width: 2,
                                height: 50,
                                displayValue: true
                            });
                        };
                        reader.readAsDataURL(file);
                    }
                });
            } else {
                uploadButton.style.display = 'none';
            }
        }
        
        // تحديث الباركود عند تغيير القيمة
        function updateBarcode(input, index) {
            const barcode = input.value;
            try {
                JsBarcode(`#barcode-${index}`, barcode, {
                    format: "EAN13",
                    lineColor: "#000",
                    width: 2,
                    height: 50,
                    displayValue: true
                });
            } catch (e) {
                alert("الباركود غير صالح. يجب أن يكون 13 رقم.");
                input.value = generateBarcode();
                updateBarcode(input, index);
            }
        }
        
        // إنشاء باركود لجميع المنتجات
        function generateAllBarcodes() {
            const barcodeInputs = document.querySelectorAll('input[name="barcodes[]"]');
            barcodeInputs.forEach((input, index) => {
                input.value = generateBarcode();
                updateBarcode(input, index);
            });
        }
        
        // تطبيق سعر التكلفة الافتراضي
        function applyDefaultCostPrice() {
            const defaultPrice = document.getElementById('default_cost_price').value;
            const priceInputs = document.querySelectorAll('input[name="cost_prices[]"]');
            priceInputs.forEach(input => {
                input.value = defaultPrice;
            });
        }
        
        // تطبيق سعر البيع الافتراضي
        function applyDefaultSellingPrice() {
            const defaultPrice = document.getElementById('default_selling_price').value;
            const priceInputs = document.querySelectorAll('input[name="selling_prices[]"]');
            priceInputs.forEach(input => {
                input.value = defaultPrice;
            });
        }
        
        // تطبيق الكمية الافتراضية
        function applyDefaultQuantity() {
            const defaultQuantity = document.getElementById('default_quantity').value;
            const quantityInputs = document.querySelectorAll('input[name="quantities[]"]');
            quantityInputs.forEach(input => {
                input.value = defaultQuantity;
            });
        }
        
        // إضافة لون جديد للمنتج
        function addColor(element) {
            const colorContainer = element.parentElement;
            const colorItem = document.createElement('div');
            colorItem.className = 'color-item';
            colorItem.innerHTML = `
                <input type="color" value="#000000" onchange="updateColorsInput(this)">
                <span class="remove-color" onclick="removeColor(this)"><i class="fas fa-times"></i></span>
            `;
            colorContainer.insertBefore(colorItem, element);
            updateColorsInput(colorItem.querySelector('input[type="color"]'));
        }
        
        // إزالة لون من المنتج
        function removeColor(element) {
            const colorItem = element.parentElement;
            const colorContainer = colorItem.parentElement;
            const colorInputs = colorContainer.querySelectorAll('input[type="color"]');
            
            // لا تسمح بإزالة اللون إذا كان هناك لون واحد فقط
            if (colorInputs.length > 1) {
                colorItem.remove();
                // تحديث حقل الألوان المخفي
                const firstColorInput = colorContainer.querySelector('input[type="color"]');
                if (firstColorInput) {
                    updateColorsInput(firstColorInput);
                }
            }
        }
        
        // تحديث حقل الألوان المخفي
        function updateColorsInput(element) {
            const colorContainer = element.closest('.color-container');
            const colorsSection = colorContainer.closest('.colors-section');
            const hiddenInput = colorsSection ? colorsSection.querySelector('input[type="hidden"]') : null;
            
            if (hiddenInput) {
                const colorInputs = colorContainer.querySelectorAll('input[type="color"]');
                const colors = Array.from(colorInputs).map(input => input.value);
                hiddenInput.value = colors.join(',');
            }
        }
        
        // إضافة لون افتراضي جديد
        function addDefaultColor() {
            const colorContainer = document.getElementById('default-colors-container');
            const addButton = colorContainer.querySelector('.add-color-btn');
            const colorItem = document.createElement('div');
            colorItem.className = 'color-item';
            colorItem.innerHTML = `
                <input type="color" value="#000000" onchange="updateDefaultColorsInput()">
                <span class="remove-color" onclick="removeDefaultColor(this)"><i class="fas fa-times"></i></span>
            `;
            colorContainer.insertBefore(colorItem, addButton);
            updateDefaultColorsInput();
        }
        
        // إزالة لون افتراضي
        function removeDefaultColor(element) {
            const colorItem = element.parentElement;
            const colorContainer = document.getElementById('default-colors-container');
            const colorInputs = colorContainer.querySelectorAll('input[type="color"]');
            
            // لا تسمح بإزالة اللون إذا كان هناك لون واحد فقط
            if (colorInputs.length > 1) {
                colorItem.remove();
                updateDefaultColorsInput();
            }
        }
        
        // تحديث حقل الألوان الافتراضية المخفي
        function updateDefaultColorsInput() {
            const colorContainer = document.getElementById('default-colors-container');
            const colorInputs = colorContainer.querySelectorAll('input[type="color"]');
            const colors = Array.from(colorInputs).map(input => input.value);
            document.getElementById('default_colors').value = colors.join(',');
        }
        
        // تطبيق الألوان الافتراضية على جميع المنتجات
        function applyDefaultColors() {
            const defaultColors = document.getElementById('default_colors').value;
            const colorContainers = document.querySelectorAll('#preview-container .color-container');
            
            colorContainers.forEach(container => {
                // إزالة جميع عناصر الألوان الحالية باستثناء زر الإضافة
                const addButton = container.querySelector('.add-color-btn');
                while (container.firstChild !== addButton) {
                    container.removeChild(container.firstChild);
                }
                
                // إضافة الألوان الافتراضية
                const colors = defaultColors.split(',');
                colors.forEach(color => {
                    const colorItem = document.createElement('div');
                    colorItem.className = 'color-item';
                    colorItem.innerHTML = `
                        <input type="color" value="${color}" onchange="updateColorsInput(this)">
                        <span class="remove-color" onclick="removeColor(this)"><i class="fas fa-times"></i></span>
                    `;
                    container.insertBefore(colorItem, addButton);
                });
                
                // تحديث حقل الألوان المخفي
                const colorsSection = container.closest('.colors-section');
                const hiddenInput = colorsSection.querySelector('input[type="hidden"]');
                hiddenInput.value = defaultColors;
            });
        }
        
        // وظيفة طباعة الصفحة
        document.getElementById('print-btn').addEventListener('click', function() {
            window.print();
        });
        
        // تحديث حقول الألوان المخفية عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateDefaultColorsInput();
        });
    </script>
</body>
</html>