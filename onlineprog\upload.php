<?php
// إنشاء مجلد الرفع إذا لم يكن موجودًا
$uploadsDir = 'uploads/';
if (!file_exists($uploadsDir)) {
    mkdir($uploadsDir, 0777, true);
}

// ملف تخزين بيانات المنتجات
$dataFile = 'products_data.json';

// تحميل البيانات الحالية إذا كانت موجودة
$productsData = [];
if (file_exists($dataFile)) {
    $productsData = json_decode(file_get_contents($dataFile), true);
}

// التحقق من وجود الملفات المرفوعة
if (isset($_FILES['image']) && isset($_POST['name']) && isset($_POST['price']) && isset($_POST['category'])) {
    $images = $_FILES['image'];
    $names = $_POST['name'];
    $prices = $_POST['price'];
    $categories = $_POST['category'];
    $quantities = isset($_POST['quantity']) ? $_POST['quantity'] : array_fill(0, count($images['name']), 1); // استخدام 1 كقيمة افتراضية إذا لم يتم توفير الكمية
    $units = isset($_POST['unit']) ? $_POST['unit'] : array_fill(0, count($images['name']), 'قطعة'); // استخدام 'قطعة' كقيمة افتراضية
    $colors = isset($_POST['colors']) ? $_POST['colors'] : array_fill(0, count($images['name']), '#000000'); // استخدام اللون الأسود كقيمة افتراضية
    
    // معالجة كل صورة
    for ($i = 0; $i < count($images['name']); $i++) {
        if ($images['error'][$i] === 0) {
            $fileName = time() . '_' . $i . '_' . basename($images['name'][$i]);
            $targetPath = $uploadsDir . $fileName;
            
            // رفع الصورة
            if (move_uploaded_file($images['tmp_name'][$i], $targetPath)) {
                // معالجة الألوان المتعددة
                $productColors = [];
                if (isset($colors[$i]) && !empty($colors[$i])) {
                    $productColors = explode(',', $colors[$i]);
                } else {
                    $productColors = ['#000000'];
                }
                
                // إضافة بيانات المنتج
                $productsData[] = [
                    'image' => $fileName,
                    'name' => $names[$i],
                    'price' => $prices[$i],
                    'category' => $categories[$i],
                    'quantity' => isset($quantities[$i]) ? intval($quantities[$i]) : 1, // التأكد من أن الكمية عدد صحيح
                    'unit' => isset($units[$i]) ? $units[$i] : 'قطعة', // إضافة وحدة القياس
                    'colors' => $productColors // إضافة الألوان المتعددة
                ];
            }
        }
    }
    
    // حفظ البيانات في ملف JSON
    file_put_contents($dataFile, json_encode($productsData, JSON_PRETTY_PRINT));
    
    // إعادة التوجيه إلى الصفحة الرئيسية
    header('Location: index.php');
    exit;
} else {
    echo 'خطأ: يرجى تحديد الصور وملء جميع الحقول المطلوبة.';
    echo '<br><a href="index.php">العودة إلى الصفحة الرئيسية</a>';
}
?>