<?php
/**
 * وظيفة إرسال إشعارات البريد الإلكتروني للعملاء
 * 
 * @param string $to البريد الإلكتروني للمستلم
 * @param string $subject عنوان الرسالة
 * @param string $message محتوى الرسالة
 * @return bool نجاح أو فشل الإرسال
 */
function sendEmailNotification($to, $subject, $message) {
    // ضبط رأس الرسالة
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= 'From: متجر المنتجات <<EMAIL>>' . "\r\n";
    
    // إرسال البريد الإلكتروني
    return mail($to, $subject, $message, $headers);
}

/**
 * إنشاء رسالة تحديث حالة الطلب
 * 
 * @param array $order بيانات الطلب
 * @param string $newStatus الحالة الجديدة
 * @return string محتوى الرسالة
 */
function createOrderStatusUpdateEmail($order, $newStatus) {
    // تحويل حالة الطلب إلى النص العربي
    $statusText = '';
    switch($newStatus) {
        case 'pending':
            $statusText = 'قيد المعالجة';
            break;
        case 'processing':
            $statusText = 'جاري التجهيز';
            break;
        case 'shipped':
            $statusText = 'تم الشحن';
            break;
        case 'delivered':
            $statusText = 'تم التسليم';
            break;
        case 'cancelled':
            $statusText = 'ملغي';
            break;
        default:
            $statusText = $newStatus;
    }
    
    // إنشاء محتوى الرسالة
    $message = '<!DOCTYPE html>
    <html dir="rtl">
    <head>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #2c3e50; color: white; padding: 15px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; }
            .order-info { margin: 20px 0; }
            .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #777; }
            .status { display: inline-block; padding: 5px 10px; border-radius: 15px; font-weight: bold; }
            .status-pending { background-color: #f39c12; color: white; }
            .status-processing { background-color: #3498db; color: white; }
            .status-shipped { background-color: #2c3e50; color: white; }
            .status-delivered { background-color: #2ecc71; color: white; }
            .status-cancelled { background-color: #e74c3c; color: white; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>تحديث حالة الطلب</h1>
            </div>
            <div class="content">
                <p>مرحباً ' . $order['customer_name'] . '،</p>
                <p>نود إعلامك بأن حالة طلبك قد تم تحديثها.</p>
                
                <div class="order-info">
                    <p><strong>رقم الطلب:</strong> ' . $order['order_id'] . '</p>
                    <p><strong>تاريخ الطلب:</strong> ' . date('d/m/Y H:i', strtotime($order['order_date'])) . '</p>
                    <p><strong>الحالة الجديدة:</strong> <span class="status status-' . $newStatus . '">' . $statusText . '</span></p>
                </div>
                
                <p>يمكنك متابعة حالة طلبك من خلال زيارة حسابك على موقعنا.</p>
                
                <p>شكراً لتسوقك معنا!</p>
            </div>
            <div class="footer">
                <p>هذه رسالة آلية، يرجى عدم الرد عليها.</p>
                <p>&copy; ' . date('Y') . ' متجر المنتجات. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </body>
    </html>';
    
    return $message;
}
?>