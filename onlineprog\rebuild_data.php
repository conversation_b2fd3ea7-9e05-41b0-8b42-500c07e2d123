<?php
// تحديد مجلد الصور
$uploadsDir = 'uploads/';
$dataFile = 'products_data.json';

// التحقق من وجود المجلد
if (!file_exists($uploadsDir)) {
    die('خطأ: مجلد الصور غير موجود!');
}

// الحصول على قائمة الملفات في المجلد
$files = scandir($uploadsDir);
$imageFiles = [];

// تصفية الملفات للحصول على الصور فقط
foreach ($files as $file) {
    // تجاهل . و ..
    if ($file === '.' || $file === '..') {
        continue;
    }
    
    // التحقق من أن الملف هو صورة
    $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
    if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'jfif'])) {
        $imageFiles[] = $file;
    }
}

// إنشاء مصفوفة لتخزين بيانات المنتجات
$productsData = [];

// استخراج معلومات من أسماء الملفات وإنشاء بيانات المنتجات
foreach ($imageFiles as $imageFile) {
    // استخراج اسم المنتج من اسم الملف (يمكن تعديل هذا حسب تنسيق أسماء الملفات)
    $nameParts = explode('_', $imageFile);
    $defaultName = 'منتج ' . (count($productsData) + 1);
    
    // تحديد القسم بناءً على نمط معين أو استخدام قسم افتراضي
    // يمكنك تعديل هذا المنطق حسب احتياجاتك
    $categories = ['شناتي', 'مطرات', 'أخرى'];
    $defaultCategory = $categories[array_rand($categories)];
    
    // إضافة المنتج إلى المصفوفة
    $productsData[] = [
        'image' => $imageFile,
        'name' => $defaultName,
        'price' => '0', // سعر افتراضي
        'category' => $defaultCategory
    ];
}

// حفظ البيانات في ملف JSON
if (file_put_contents($dataFile, json_encode($productsData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
    echo '<div style="background-color: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    echo '<h2>تم إعادة إنشاء ملف البيانات بنجاح!</h2>';
    echo '<p>تم العثور على ' . count($imageFiles) . ' صورة وإضافتها إلى ملف البيانات.</p>';
    echo '<p><strong>ملاحظة مهمة:</strong> تم تعيين أسماء وأسعار افتراضية للمنتجات. يرجى تحديث هذه المعلومات يدويًا من خلال صفحة التعديل.</p>';
    echo '<p><a href="index.php" style="color: #155724; font-weight: bold;">العودة إلى الصفحة الرئيسية</a></p>';
    echo '</div>';
} else {
    echo '<div style="background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    echo '<h2>خطأ!</h2>';
    echo '<p>لم يتم إنشاء ملف البيانات. تأكد من أن المجلد لديه صلاحيات الكتابة.</p>';
    echo '</div>';
}
?>