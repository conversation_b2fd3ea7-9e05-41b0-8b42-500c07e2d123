<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض تفاصيل الطلبية</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Se<PERSON>e <PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 0;
            margin: 0;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem 0;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.2rem;
            margin: 0;
            font-weight: 600;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px 50px;
        }
        
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .card-header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem 1.5rem;
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .card-header i {
            margin-left: 10px;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .btn {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            border: none;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            transition: var(--transition);
            margin-right: 0.5rem;
            font-size: 0.9rem;
        }
        
        .btn-primary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #2980b9;
        }
        
        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        
        .btn-success:hover {
            background-color: #27ae60;
        }
        
        .action-buttons-top {
            display: flex;
            justify-content: flex-start;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .order-info {
            margin-bottom: 2rem;
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: var(--border-radius);
            border-left: 4px solid var(--secondary-color);
        }
        
        .order-info p {
            margin: 0.5rem 0;
        }
        
        .product-list {
            margin-top: 2rem;
        }
        
        .product-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #eee;
            position: relative;
        }
        
        .product-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: var(--border-radius);
            margin-left: 1rem;
        }
        
        .product-details {
            flex: 1;
        }
        
        .product-name {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }
        
        .product-info {
            display: flex;
            flex-wrap: wrap;
            gap: 0.8rem;
            font-size: 0.9rem;
            color: #666;
        }
        
        .info-item {
            display: inline-flex;
            align-items: center;
        }
        
        .info-item i {
            margin-left: 0.3rem;
            color: var(--secondary-color);
        }
        
        .total-section {
            margin-top: 2rem;
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: var(--border-radius);
            border-left: 4px solid var(--success-color);
        }
        
        .total-section p {
            margin: 0.5rem 0;
            font-size: 1.1rem;
        }
        
        .total-section .total-profit {
            font-weight: 600;
            color: var(--success-color);
        }
        
        .print-section {
            margin-top: 2rem;
            text-align: center;
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            text-align: center;
            padding: 1.5rem 0;
            margin-top: 3rem;
        }
        
        @media print {
            .header, .action-buttons-top, .footer, .print-section {
                display: none;
            }
            
            .container {
                width: 100%;
                max-width: 100%;
                padding: 0;
                margin: 0;
            }
            
            .card {
                box-shadow: none;
                border: none;
            }
            
            .card-header {
                background-color: #f8f9fa;
                color: #333;
                border-bottom: 1px solid #ddd;
            }
        }
        
        @media (max-width: 768px) {
            .action-buttons-top {
                flex-direction: column;
            }
            
            .product-info {
                flex-direction: column;
                gap: 0.4rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>تفاصيل الطلبية</h1>
    </div>
    
    <div class="container">
        <!-- أزرار الإجراءات في أعلى الصفحة -->
        <div class="action-buttons-top">
            <a href="orders.php" class="btn btn-primary">
                <i class="fas fa-arrow-right"></i> العودة إلى قائمة الطلبيات
            </a>
            <button onclick="window.print()" class="btn btn-success">
                <i class="fas fa-print"></i> طباعة الطلبية
            </button>
        </div>
        
        <?php
        // ملف تخزين الطلبيات
        $ordersFile = 'orders_data.json';
        
        // التحقق من وجود معرف الطلبية
        if (isset($_GET['id']) && file_exists($ordersFile)) {
            $orderId = $_GET['id'];
            $ordersData = json_decode(file_get_contents($ordersFile), true);
            
            // البحث عن الطلبية بواسطة المعرف
            $orderFound = false;
            $orderDetails = null;
            
            foreach ($ordersData as $order) {
                if ($order['order_id'] == $orderId) {
                    $orderDetails = $order;
                    $orderFound = true;
                    break;
                }
            }
            
            if ($orderFound) {
                // عرض معلومات الطلبية
                echo '<div class="card">';
                echo '<div class="card-header">';
                echo '<span><i class="fas fa-info-circle"></i> معلومات الطلبية</span>';
                echo '</div>';
                echo '<div class="card-body">';
                
                echo '<div class="order-info">';
                echo '<p><strong>رقم الطلبية:</strong> ' . $orderDetails['order_id'] . '</p>';
                echo '<p><strong>تاريخ الطلبية:</strong> ' . $orderDetails['order_date'] . '</p>';
                echo '<p><strong>عدد المنتجات:</strong> ' . count($orderDetails['products']) . '</p>';
                echo '</div>';
                
                // عرض قائمة المنتجات
                echo '<div class="product-list">';
                echo '<h3>قائمة المنتجات</h3>';
                
                foreach ($orderDetails['products'] as $product) {
                    $totalPrice = $product['sellingPrice'] * $product['quantity'];
                    
                    echo '<div class="product-item">';
                    echo '<img src="' . $product['image'] . '" alt="' . $product['name'] . '" class="product-image">';
                    echo '<div class="product-details">';
                    echo '<div class="product-name">' . $product['name'] . '</div>';
                    echo '<div class="product-info">';
                    echo '<span class="info-item"><i class="fas fa-barcode"></i> الباركود: ' . ($product['barcode'] ?? 'غير محدد') . '</span>';
                    echo '<span class="info-item"><i class="fas fa-money-bill-wave"></i> سعر التكلفة: ' . $product['costPrice'] . ' ريال</span>';
                    echo '<span class="info-item"><i class="fas fa-money-bill-wave"></i> سعر البيع: ' . $product['sellingPrice'] . ' ريال</span>';
                    echo '<span class="info-item"><i class="fas fa-sort-amount-up"></i> الكمية: ' . $product['quantity'] . '</span>';
                    echo '<span class="info-item"><i class="fas fa-calculator"></i> الإجمالي: ' . $totalPrice . ' ريال</span>';
                    echo '</div>';
                    echo '</div>';
                    echo '</div>';
                }
                
                echo '</div>';
                
                // عرض إجماليات الطلبية
                echo '<div class="total-section">';
                echo '<p><strong>إجمالي التكلفة:</strong> ' . $orderDetails['total_cost'] . ' ريال</p>';
                echo '<p><strong>إجمالي البيع:</strong> ' . $orderDetails['total_selling'] . ' ريال</p>';
                echo '<p class="total-profit"><strong>الربح المتوقع:</strong> ' . $orderDetails['total_profit'] . ' ريال</p>';
                echo '</div>';
                
                echo '<div class="print-section">';
                echo '<button onclick="window.print()" class="btn btn-success">';
                echo '<i class="fas fa-print"></i> طباعة الطلبية';
                echo '</button>';
                echo '</div>';
                
                echo '</div>';
                echo '</div>';
            } else {
                echo '<div class="card">';
                echo '<div class="card-body">';
                echo '<p>الطلبية غير موجودة أو تم حذفها.</p>';
                echo '</div>';
                echo '</div>';
            }
        } else {
            echo '<div class="card">';
            echo '<div class="card-body">';
            echo '<p>معرف الطلبية غير صحيح أو ملف الطلبيات غير موجود.</p>';
            echo '</div>';
            echo '</div>';
        }
        ?>
    </div>
    
    <div class="footer">
        <p>جميع الحقوق محفوظة &copy; <?php echo date('Y'); ?> نظام إدارة المنتجات</p>
    </div>
</body>
</html>