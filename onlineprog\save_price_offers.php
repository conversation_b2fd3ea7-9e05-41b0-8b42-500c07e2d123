<?php
// إنشاء مجلد الرفع إذا لم يكن موجودًا
$uploadsDir = 'uploads/';
if (!file_exists($uploadsDir)) {
    mkdir($uploadsDir, 0777, true);
}

// ملف تخزين بيانات المنتجات
$dataFile = 'products_data.json';

// تحميل البيانات الحالية إذا كانت موجودة
$productsData = [];
if (file_exists($dataFile)) {
    $productsData = json_decode(file_get_contents($dataFile), true);
}

// التحقق من وجود الملفات المرفوعة
if (isset($_FILES['images']) && isset($_POST['names'])) {
    $images = $_FILES['images'];
    $names = $_POST['names'];
    $costPrices = isset($_POST['cost_prices']) ? $_POST['cost_prices'] : [];
    $sellingPrices = isset($_POST['selling_prices']) ? $_POST['selling_prices'] : [];
    $quantities = isset($_POST['quantities']) ? $_POST['quantities'] : [];
    $colors = isset($_POST['colors']) ? $_POST['colors'] : [];
    $barcodes = isset($_POST['barcodes']) ? $_POST['barcodes'] : [];
    
    // معالجة كل صورة
    for ($i = 0; $i < count($images['name']); $i++) {
        if ($images['error'][$i] === 0) {
            $fileName = time() . '_' . $i . '_' . basename($images['name'][$i]);
            $targetPath = $uploadsDir . $fileName;
            
            // رفع الصورة
            if (move_uploaded_file($images['tmp_name'][$i], $targetPath)) {
                // معالجة الألوان المتعددة
                $productColors = [];
                if (isset($colors[$i]) && !empty($colors[$i])) {
                    $productColors = explode(',', $colors[$i]);
                } else {
                    $productColors = ['#000000'];
                }
                
                // إضافة بيانات المنتج
                $productsData[] = [
                    'image' => $fileName,
                    'name' => isset($names[$i]) ? $names[$i] : 'صنف ' . ($i + 1),
                    'price' => isset($sellingPrices[$i]) ? $sellingPrices[$i] : '0',
                    'cost_price' => isset($costPrices[$i]) ? $costPrices[$i] : '0',
                    'selling_price' => isset($sellingPrices[$i]) ? $sellingPrices[$i] : '0',
                    'quantity' => isset($quantities[$i]) ? intval($quantities[$i]) : 1,
                    'category' => 'عروض الأسعار',
                    'colors' => $productColors,
                    'barcode' => isset($barcodes[$i]) ? $barcodes[$i] : ''
                ];
            }
        }
    }
    
    // حفظ البيانات في ملف JSON
    file_put_contents($dataFile, json_encode($productsData, JSON_PRETTY_PRINT));
    
    // إعادة التوجيه إلى الصفحة الرئيسية
    header('Location: index.php');
    exit;
} else {
    echo 'خطأ: يرجى تحديد الصور وملء جميع الحقول المطلوبة.';
    echo '<br><a href="index.php">العودة إلى الصفحة الرئيسية</a>';
}
?>