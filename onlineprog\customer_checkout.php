<?php
session_start();

// التحقق من وجود سلة التسوق وأنها غير فارغة
if (!isset($_SESSION['cart']) || empty($_SESSION['cart'])) {
    header('Location: customer_cart.php');
    exit;
}

// التحقق من تسجيل دخول المستخدم
if (!isset($_SESSION['customer_id'])) {
    header('Location: customer_login.php?redirect=checkout');
    exit;
}

// تحميل بيانات العميل
$customer = null;
if (file_exists('customer_data.json')) {
    $customers = json_decode(file_get_contents('customer_data.json'), true);
    foreach ($customers as $c) {
        if ($c['id'] == $_SESSION['customer_id']) {
            $customer = $c;
            break;
        }
    }
}

if (!$customer) {
    // إذا لم يتم العثور على بيانات العميل، قم بتسجيل الخروج وإعادة التوجيه
    session_unset();
    session_destroy();
    header('Location: customer_login.php');
    exit;
}

$error = '';
$success = '';

// حساب المجموع الكلي
$totalAmount = 0;
foreach ($_SESSION['cart'] as $item) {
    $totalAmount += $item['price'] * $item['quantity'];
}

// معالجة نموذج إتمام الطلب
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name']);
    $phone = trim($_POST['phone']);
    $address = trim($_POST['address']);
    $notes = trim($_POST['notes']);
    $payment_method = $_POST['payment_method'];
    
    // التحقق من البيانات المطلوبة
    if (empty($name) || empty($phone) || empty($address)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } else {
        // إنشاء معرف فريد للطلب
        $order_id = time();
        
        // إعداد بيانات الطلب
        $order = [
            'order_id' => $order_id,
            'customer_id' => $_SESSION['customer_id'],
            'customer_name' => $name,
            'customer_phone' => $phone,
            'customer_address' => $address,
            'customer_notes' => $notes,
            'payment_method' => $payment_method,
            'order_date' => date('Y-m-d H:i:s'),
            'status' => 'pending', // قيد المعالجة
            'items' => $_SESSION['cart'],
            'total_amount' => $totalAmount
        ];
        
        // تحميل طلبيات العملاء الحالية
        $orders = [];
        if (file_exists('customer_orders.json')) {
            $orders = json_decode(file_get_contents('customer_orders.json'), true);
        }
        
        // إضافة الطلب الجديد
        $orders[] = $order;
        
        // حفظ البيانات في الملف
        if (file_put_contents('customer_orders.json', json_encode($orders, JSON_PRETTY_PRINT))) {
            // إفراغ سلة التسوق
            $_SESSION['cart'] = [];
            
            // إرسال إشعار واتساب للعميل
            require_once 'send_whatsapp_notification.php';
            $whatsappMessage = createOrderConfirmationWhatsAppMessage($order);
            $whatsappLink = sendWhatsAppNotification($phone, $whatsappMessage);
            
            // عرض رسالة نجاح وإعادة التوجيه
            $success = 'تم إتمام الطلب بنجاح! رقم الطلب: ' . $order_id;
            $_SESSION['whatsapp_link'] = $whatsappLink; // تخزين رابط الواتساب في الجلسة
            header('refresh:3;url=customer_order_details.php?id=' . $order_id);
        } else {
            $error = 'حدث خطأ أثناء معالجة الطلب. يرجى المحاولة مرة أخرى';
        }
    }
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إتمام الطلب - متجر المنتجات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 0;
            margin: 0;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem 0;
            text-align: center;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.2rem;
            margin: 0;
            font-weight: 600;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px 50px;
        }
        
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            margin-bottom: 20px;
        }
        
        .nav-links {
            display: flex;
            gap: 15px;
        }
        
        .nav-link {
            text-decoration: none;
            color: var(--dark-color);
            font-weight: 600;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .nav-link:hover {
            color: var(--secondary-color);
        }
        
        .checkout-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .checkout-container {
                grid-template-columns: 1fr;
            }
        }
        
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .card-header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem 1.5rem;
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .card-header i {
            margin-left: 10px;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: var(--transition);
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .form-group input:focus, .form-group textarea:focus, .form-group select:focus {
            border-color: var(--secondary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }
        
        .error-message {
            color: var(--danger-color);
            margin-bottom: 1rem;
            font-weight: 600;
            padding: 10px;
            background-color: rgba(231, 76, 60, 0.1);
            border-radius: var(--border-radius);
            display: <?php echo !empty($error) ? 'block' : 'none'; ?>;
        }
        
        .success-message {
            color: var(--success-color);
            margin-bottom: 1rem;
            font-weight: 600;
            padding: 10px;
            background-color: rgba(46, 204, 113, 0.1);
            border-radius: var(--border-radius);
            display: <?php echo !empty($success) ? 'block' : 'none'; ?>;
        }
        
        .cart-items {
            margin-bottom: 1.5rem;
        }
        
        .cart-item {
            display: flex;
            align-items: center;
            padding: 0.8rem 0;
            border-bottom: 1px solid var(--light-color);
        }
        
        .cart-item:last-child {
            border-bottom: none;
        }
        
        .item-image {
            width: 60px;
            height: 60px;
            border-radius: var(--border-radius);
            overflow: hidden;
            margin-left: 1rem;
        }
        
        .item-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .item-details {
            flex: 1;
        }
        
        .item-name {
            font-weight: 600;
            margin-bottom: 0.3rem;
        }
        
        .item-price {
            color: var(--secondary-color);
            font-size: 0.9rem;
        }
        
        .item-quantity {
            font-size: 0.9rem;
            color: #666;
        }
        
        .item-total {
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .cart-summary {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-top: 1rem;
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .summary-row.total {
            font-weight: 700;
            font-size: 1.2rem;
            color: var(--dark-color);
            border-top: 1px solid #ddd;
            padding-top: 0.5rem;
            margin-top: 0.5rem;
        }
        
        .place-order-btn {
            background-color: var(--success-color);
            color: white;
            border: none;
            padding: 12px;
            width: 100%;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            transition: var(--transition);
            margin-top: 1rem;
        }
        
        .place-order-btn:hover {
            background-color: #27ae60;
        }
        
        .payment-options {
            margin-top: 1rem;
        }
        
        .payment-option {
            display: flex;
            align-items: center;
            margin-bottom: 0.8rem;
        }
        
        .payment-option input[type="radio"] {
            margin-left: 10px;
            width: auto;
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            text-align: center;
            padding: 1.5rem 0;
            margin-top: 3rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>إتمام الطلب</h1>
    </div>
    
    <div class="container">
        <div class="navbar">
            <div class="nav-links">
                <a href="customer_products.php" class="nav-link"><i class="fas fa-home"></i> الرئيسية</a>
                <a href="customer_orders.php" class="nav-link"><i class="fas fa-box"></i> طلبياتي</a>
                <a href="customer_cart.php" class="nav-link"><i class="fas fa-shopping-cart"></i> سلة التسوق</a>
            </div>
            
            <a href="customer_logout.php" class="nav-link">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </a>
        </div>
        
        <div class="error-message"><?php echo $error; ?></div>
        <div class="success-message"><?php echo $success; ?></div>
        
        <?php if (empty($success)): ?>
        <form method="post" action="">
            <div class="checkout-container">
                <div class="card">
                    <div class="card-header">
                        <span><i class="fas fa-user"></i> معلومات الشحن</span>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="name">الاسم الكامل</label>
                            <input type="text" id="name" name="name" required value="<?php echo $customer['name']; ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="phone">رقم الهاتف</label>
                            <input type="tel" id="phone" name="phone" required value="<?php echo $customer['phone']; ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="address">عنوان الشحن</label>
                            <textarea id="address" name="address" required><?php echo $customer['address']; ?></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="notes">ملاحظات إضافية (اختياري)</label>
                            <textarea id="notes" name="notes"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label>طريقة الدفع</label>
                            <div class="payment-options">
                                <div class="payment-option">
                                    <input type="radio" id="cash" name="payment_method" value="cash" checked>
                                    <label for="cash">الدفع عند الاستلام</label>
                                </div>
                                <div class="payment-option">
                                    <input type="radio" id="bank_transfer" name="payment_method" value="bank_transfer">
                                    <label for="bank_transfer">تحويل بنكي</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <span><i class="fas fa-shopping-cart"></i> ملخص الطلب</span>
                    </div>
                    <div class="card-body">
                        <div class="cart-items">
                            <?php foreach ($_SESSION['cart'] as $item): ?>
                                <div class="cart-item">
                                    <div class="item-image">
                                        <?php if (!empty($item['image'])): ?>
                                            <img src="<?php echo $item['image']; ?>" alt="<?php echo $item['name']; ?>">
                                        <?php else: ?>
                                            <img src="https://via.placeholder.com/60" alt="صورة المنتج">
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="item-details">
                                        <div class="item-name"><?php echo $item['name']; ?></div>
                                        <div class="item-price"><?php echo $item['price']; ?> شيقل × <?php echo $item['quantity']; ?></div>
                                    </div>
                                    
                                    <div class="item-total">
                                        <?php echo $item['price'] * $item['quantity']; ?> شيقل
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="cart-summary">
                            <div class="summary-row total">
                                <span>المجموع الكلي:</span>
                                <span><?php echo $totalAmount; ?> شيقل</span>
                            </div>
                        </div>
                        
                        <button type="submit" class="place-order-btn">
                            <i class="fas fa-check-circle"></i> تأكيد الطلب
                        </button>
                    </div>
                </div>
            </div>
        </form>
        <?php endif; ?>
    </div>
    
    <div class="footer">
        <p>جميع الحقوق محفوظة &copy; <?php echo date('Y'); ?> متجر المنتجات</p>
    </div>
</body>
</html>