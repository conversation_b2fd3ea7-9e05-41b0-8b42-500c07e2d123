<?php
// ملف تخزين الطلبيات
$ordersFile = 'orders_data.json';

// التحقق من وجود معرف الطلبية
if (isset($_GET['id']) && file_exists($ordersFile)) {
    $orderId = $_GET['id'];
    $ordersData = json_decode(file_get_contents($ordersFile), true);
    
    // البحث عن الطلبية بواسطة المعرف وحذفها
    $newOrdersData = [];
    $orderDeleted = false;
    
    foreach ($ordersData as $order) {
        if ($order['order_id'] != $orderId) {
            $newOrdersData[] = $order;
        } else {
            $orderDeleted = true;
        }
    }
    
    // حفظ البيانات الجديدة بعد الحذف
    if ($orderDeleted) {
        file_put_contents($ordersFile, json_encode($newOrdersData, JSON_PRETTY_PRINT));
    }
}

// إعادة التوجيه إلى صفحة الطلبيات
header('Location: orders.php');
exit;
?>