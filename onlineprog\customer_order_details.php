<?php
session_start();

// التحقق من تسجيل دخول المستخدم
if (!isset($_SESSION['customer_id'])) {
    header('Location: customer_login.php');
    exit;
}

// التحقق من وجود معرف الطلب
if (!isset($_GET['id'])) {
    header('Location: customer_orders.php');
    exit;
}

$order_id = $_GET['id'];
$order = null;
$customer_id = $_SESSION['customer_id'];

// تحميل بيانات الطلبيات
if (file_exists('customer_orders.json')) {
    $all_orders = json_decode(file_get_contents('customer_orders.json'), true);
    
    // البحث عن الطلب المحدد
    foreach ($all_orders as $o) {
        if ($o['order_id'] == $order_id && $o['customer_id'] == $customer_id) {
            $order = $o;
            break;
        }
    }
}

// إذا لم يتم العثور على الطلب أو كان الطلب لمستخدم آخر
if (!$order) {
    header('Location: customer_orders.php');
    exit;
}

// حساب المجموع الكلي (للتأكد)
$total_amount = 0;
foreach ($order['items'] as $item) {
    $total_amount += $item['price'] * $item['quantity'];
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الطلب #<?php echo $order_id; ?> - متجر المنتجات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 0;
            margin: 0;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem 0;
            text-align: center;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.2rem;
            margin: 0;
            font-weight: 600;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px 50px;
        }
        
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            margin-bottom: 20px;
        }
        
        .nav-links {
            display: flex;
            gap: 15px;
        }
        
        .nav-link {
            text-decoration: none;
            color: var(--dark-color);
            font-weight: 600;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .nav-link:hover {
            color: var(--secondary-color);
        }
        
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .card-header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem 1.5rem;
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .card-header i {
            margin-left: 10px;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .order-info {
            background-color: #f8f9fa;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
            border-right: 4px solid var(--secondary-color);
        }
        
        .order-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1rem;
        }
        
        .info-group {
            margin-bottom: 1rem;
        }
        
        .info-label {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 0.3rem;
            display: block;
        }
        
        .info-value {
            color: #555;
        }
        
        .order-status {
            display: inline-block;
            padding: 0.3rem 0.6rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-pending {
            background-color: var(--warning-color);
            color: white;
        }
        
        .status-processing {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .status-shipped {
            background-color: var(--primary-color);
            color: white;
        }
        
        .status-delivered {
            background-color: var(--success-color);
            color: white;
        }
        
        .status-cancelled {
            background-color: var(--danger-color);
            color: white;
        }
        
        .order-items {
            margin-bottom: 2rem;
        }
        
        .order-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--light-color);
        }
        
        .order-item:last-child {
            border-bottom: none;
        }
        
        .item-image {
            width: 80px;
            height: 80px;
            border-radius: var(--border-radius);
            overflow: hidden;
            margin-left: 1rem;
        }
        
        .item-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .item-details {
            flex: 1;
        }
        
        .item-name {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }
        
        .item-price {
            color: var(--secondary-color);
            font-weight: 600;
            margin-bottom: 0.3rem;
        }
        
        .item-quantity {
            color: #666;
            font-size: 0.9rem;
        }
        
        .item-total {
            font-weight: 600;
            color: var(--dark-color);
            text-align: left;
            padding-right: 1rem;
        }
        
        .order-summary {
            background-color: #f8f9fa;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border-right: 4px solid var(--success-color);
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .summary-row.total {
            font-weight: 700;
            font-size: 1.3rem;
            color: var(--dark-color);
            border-top: 1px solid #ddd;
            padding-top: 1rem;
        }
        
        .back-btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
            margin-top: 1rem;
        }
        
        .back-btn:hover {
            background-color: #1e2b38;
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            text-align: center;
            padding: 1.5rem 0;
            margin-top: 3rem;
        }
        
        @media (max-width: 768px) {
            .order-info-grid {
                grid-template-columns: 1fr;
            }
            
            .order-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .item-image {
                margin-bottom: 1rem;
                margin-left: 0;
            }
            
            .item-total {
                text-align: right;
                width: 100%;
                padding-right: 0;
                margin-top: 0.5rem;
            }
            
            .navbar {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>تفاصيل الطلب #<?php echo $order_id; ?></h1>
    </div>
    
    <div class="container">
        <div class="navbar">
            <div class="nav-links">
                <a href="customer_products.php" class="nav-link"><i class="fas fa-home"></i> الرئيسية</a>
                <a href="customer_orders.php" class="nav-link"><i class="fas fa-box"></i> طلبياتي</a>
                <a href="customer_cart.php" class="nav-link"><i class="fas fa-shopping-cart"></i> سلة التسوق</a>
            </div>
            
            <a href="customer_logout.php" class="nav-link">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </a>
        </div>
        
        <div class="card">
            <div class="card-header">
                <span><i class="fas fa-info-circle"></i> معلومات الطلب</span>
                <span class="order-status status-<?php echo $order['status']; ?>">
                    <?php 
                    $status_text = '';
                    switch($order['status']) {
                        case 'pending':
                            $status_text = 'قيد المعالجة';
                            break;
                        case 'processing':
                            $status_text = 'جاري التجهيز';
                            break;
                        case 'shipped':
                            $status_text = 'تم الشحن';
                            break;
                        case 'delivered':
                            $status_text = 'تم التسليم';
                            break;
                        case 'cancelled':
                            $status_text = 'ملغي';
                            break;
                        default:
                            $status_text = $order['status'];
                    }
                    echo $status_text;
                    ?>
                </span>
            </div>
            <div class="card-body">
                <div class="order-info">
                    <div class="order-info-grid">
                        <div class="info-group">
                            <span class="info-label">رقم الطلب:</span>
                            <span class="info-value"><?php echo $order['order_id']; ?></span>
                        </div>
                        
                        <div class="info-group">
                            <span class="info-label">تاريخ الطلب:</span>
                            <span class="info-value"><?php echo date('d/m/Y H:i', strtotime($order['order_date'])); ?></span>
                        </div>
                        
                        <div class="info-group">
                            <span class="info-label">اسم المستلم:</span>
                            <span class="info-value"><?php echo $order['customer_name']; ?></span>
                        </div>
                        
                        <div class="info-group">
                            <span class="info-label">رقم الهاتف:</span>
                            <span class="info-value"><?php echo $order['customer_phone']; ?></span>
                        </div>
                        
                        <div class="info-group">
                            <span class="info-label">عنوان الشحن:</span>
                            <span class="info-value"><?php echo $order['customer_address']; ?></span>
                        </div>
                        
                        <div class="info-group">
                            <span class="info-label">طريقة الدفع:</span>
                            <span class="info-value">
                                <?php echo $order['payment_method'] == 'cash' ? 'الدفع عند الاستلام' : 'تحويل بنكي'; ?>
                            </span>
                        </div>
                    </div>
                    
                    <?php if (!empty($order['customer_notes'])): ?>
                    <div class="info-group" style="margin-top: 1rem;">
                        <span class="info-label">ملاحظات إضافية:</span>
                        <span class="info-value"><?php echo $order['customer_notes']; ?></span>
                    </div>
                    <?php endif; ?>
                </div>
                
                <h3 style="margin-bottom: 1rem;"><i class="fas fa-shopping-basket"></i> المنتجات المطلوبة</h3>
                
                <div class="order-items">
                    <?php foreach ($order['items'] as $item): ?>
                        <div class="order-item">
                            <div class="item-image">
                                <?php if (!empty($item['image'])): ?>
                                    <img src="<?php echo $item['image']; ?>" alt="<?php echo $item['name']; ?>">
                                <?php else: ?>
                                    <img src="https://via.placeholder.com/80" alt="صورة المنتج">
                                <?php endif; ?>
                            </div>
                            
                            <div class="item-details">
                                <div class="item-name"><?php echo $item['name']; ?></div>
                                <div class="item-price"><?php echo $item['price']; ?> شيقل</div>
                                <div class="item-quantity">الكمية: <?php echo $item['quantity']; ?></div>
                            </div>
                            
                            <div class="item-total">
                                <?php echo $item['price'] * $item['quantity']; ?> شيقل
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="order-summary">
                    <div class="summary-row total">
                        <span>المجموع الكلي:</span>
                        <span><?php echo $order['total_amount']; ?> شيقل</span>
                    </div>
                </div>
                
                <div class="order-actions">
                    <a href="customer_orders.php" class="btn btn-secondary"><i class="fas fa-arrow-right"></i> العودة للطلبيات</a>
                    
                    <?php if (isset($_SESSION['whatsapp_link'])): ?>
                    <a href="<?php echo $_SESSION['whatsapp_link']; ?>" target="_blank" class="btn btn-success">
                        <i class="fab fa-whatsapp"></i> متابعة الطلب عبر واتساب
                    </a>
                    <?php unset($_SESSION['whatsapp_link']); // حذف الرابط بعد العرض ?>
                    <?php else: ?>
                    <?php 
                        // إنشاء رابط واتساب مباشرة إذا لم يكن موجودًا في الجلسة
                        require_once 'send_whatsapp_notification.php';
                        $whatsappMessage = createOrderConfirmationWhatsAppMessage($order);
                        $whatsappLink = sendWhatsAppNotification($order['customer_phone'], $whatsappMessage);
                    ?>
                    <a href="<?php echo $whatsappLink; ?>" target="_blank" class="btn btn-success">
                        <i class="fab fa-whatsapp"></i> متابعة الطلب عبر واتساب
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>جميع الحقوق محفوظة &copy; <?php echo date('Y'); ?> متجر المنتجات</p>
    </div>
</body>
</html>