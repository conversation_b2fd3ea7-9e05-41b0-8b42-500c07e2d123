<?php
/**
 * وظيفة إرسال إشعارات واتساب للعملاء
 * 
 * @param string $phone رقم هاتف المستلم
 * @param string $message محتوى الرسالة
 * @return string رابط واتساب
 */
function sendWhatsAppNotification($phone, $message) {
    // تنظيف رقم الهاتف (إزالة المسافات والرموز)
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // التأكد من أن رقم الهاتف يبدأ بـ 9xx (إذا لم يكن يحتوي على رمز الدولة)
    if (strlen($phone) == 9 && substr($phone, 0, 1) != '9') {
        $phone = '9' . $phone;
    }
    
    // إضافة رمز الدولة إذا لم يكن موجودًا (مثال: 970 لفلسطين)
    if (strlen($phone) == 9) {
        $phone = '970' . $phone;
    }
    
    // إنشاء رابط واتساب
    $whatsappLink = 'https://wa.me/' . $phone . '?text=' . urlencode($message);
    
    return $whatsappLink;
}

/**
 * إنشاء رسالة تأكيد الطلب للواتساب
 * 
 * @param array $order بيانات الطلب
 * @return string محتوى الرسالة
 */
function createOrderConfirmationWhatsAppMessage($order) {
    // تحويل حالة الطلب إلى النص العربي
    $statusText = '';
    switch($order['status']) {
        case 'pending':
            $statusText = 'قيد المعالجة';
            break;
        case 'processing':
            $statusText = 'جاري التجهيز';
            break;
        case 'shipped':
            $statusText = 'تم الشحن';
            break;
        case 'delivered':
            $statusText = 'تم التسليم';
            break;
        case 'cancelled':
            $statusText = 'ملغي';
            break;
        default:
            $statusText = $order['status'];
    }
    
    // إنشاء محتوى الرسالة
    $message = "مرحباً {$order['customer_name']}،\n\n";
    $message .= "شكراً لطلبك من متجرنا!\n";
    $message .= "تم استلام طلبك بنجاح وهو الآن {$statusText}.\n\n";
    $message .= "معلومات الطلب:\n";
    $message .= "رقم الطلب: {$order['order_id']}\n";
    $message .= "تاريخ الطلب: " . date('d/m/Y H:i', strtotime($order['order_date'])) . "\n";
    $message .= "إجمالي المبلغ: " . number_format($order['total_amount'], 2) . " ₪\n\n";
    
    $message .= "المنتجات المطلوبة:\n";
    foreach ($order['items'] as $item) {
        $message .= "- {$item['name']} (الكمية: {$item['quantity']}) - " . number_format($item['price'] * $item['quantity'], 2) . " ₪\n";
    }
    
    $message .= "\nيمكنك متابعة حالة طلبك من خلال زيارة حسابك على موقعنا.\n";
    $message .= "شكراً لتسوقك معنا!";
    
    return $message;
}

/**
 * إنشاء رسالة تحديث حالة الطلب للواتساب
 * 
 * @param array $order بيانات الطلب
 * @param string $newStatus الحالة الجديدة
 * @return string محتوى الرسالة
 */
function createOrderStatusUpdateWhatsAppMessage($order, $newStatus) {
    // تحويل حالة الطلب إلى النص العربي
    $statusText = '';
    switch($newStatus) {
        case 'pending':
            $statusText = 'قيد المعالجة';
            break;
        case 'processing':
            $statusText = 'جاري التجهيز';
            break;
        case 'shipped':
            $statusText = 'تم الشحن';
            break;
        case 'delivered':
            $statusText = 'تم التسليم';
            break;
        case 'cancelled':
            $statusText = 'ملغي';
            break;
        default:
            $statusText = $newStatus;
    }
    
    // إنشاء محتوى الرسالة
    $message = "مرحباً {$order['customer_name']}،\n\n";
    $message .= "نود إعلامك بأن حالة طلبك قد تم تحديثها.\n\n";
    $message .= "معلومات الطلب:\n";
    $message .= "رقم الطلب: {$order['order_id']}\n";
    $message .= "تاريخ الطلب: " . date('d/m/Y H:i', strtotime($order['order_date'])) . "\n";
    $message .= "الحالة الجديدة: {$statusText}\n\n";
    
    $message .= "يمكنك متابعة حالة طلبك من خلال زيارة حسابك على موقعنا.\n";
    $message .= "شكراً لتسوقك معنا!";
    
    return $message;
}
?>