<?php
require_once 'admin_auth.php';

// التحقق من تسجيل الدخول
requireLogin();

// تحميل بيانات المنتجات
$productsFile = 'products_data.json';
$products = [];

if (file_exists($productsFile)) {
    $products = json_decode(file_get_contents($productsFile), true);
}

// تحميل بيانات الأقسام
$categoriesFile = 'categories_data.json';
$categories = [];

if (file_exists($categoriesFile)) {
    $categories = json_decode(file_get_contents($categoriesFile), true);
} else {
    // استخراج الأقسام من المنتجات إذا لم يكن ملف الأقسام موجوداً
    $uniqueCategories = [];
    $id = 1;
    
    foreach ($products as $product) {
        if (isset($product['category']) && !in_array($product['category'], array_column($uniqueCategories, 'name'))) {
            $uniqueCategories[] = [
                'id' => $id++,
                'name' => $product['category'],
                'image' => 'placeholder.jpg',
                'active' => true
            ];
        }
    }
    
    $categories = $uniqueCategories;
}

// معالجة حذف منتج
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $productToDelete = $_GET['delete'];
    
    foreach ($products as $key => $product) {
        if ($product['name'] === $productToDelete) {
            // حذف صورة المنتج إذا كانت موجودة
            if (isset($product['image']) && file_exists('uploads/' . $product['image'])) {
                unlink('uploads/' . $product['image']);
            }
            
            // حذف المنتج من المصفوفة
            unset($products[$key]);
            break;
        }
    }
    
    // إعادة ترتيب المصفوفة
    $products = array_values($products);
    
    // حفظ التغييرات
    file_put_contents($productsFile, json_encode($products, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    
    // إعادة التوجيه مع رسالة نجاح
    header('Location: admin_products.php?deleted=1');
    exit;
}

// تنظيم المنتجات حسب الأقسام
$productsByCategory = [];

foreach ($products as $product) {
    $category = isset($product['category']) ? $product['category'] : 'بدون قسم';
    
    if (!isset($productsByCategory[$category])) {
        $productsByCategory[$category] = [];
    }
    
    $productsByCategory[$category][] = $product;
}

// الحصول على القسم المحدد (إذا تم تحديده)
$selectedCategory = isset($_GET['category']) ? $_GET['category'] : '';
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - نظام إدارة المنتجات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* Header Styles */
        .header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }
        
        .header h1 {
            font-size: 1.5rem;
            margin: 0;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .user-info span {
            margin-left: 10px;
        }
        
        .user-info a {
            color: white;
            text-decoration: none;
            margin-right: 15px;
            opacity: 0.8;
            transition: var(--transition);
        }
        
        .user-info a:hover {
            opacity: 1;
        }
        
        /* Navigation */
        .nav {
            display: flex;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .nav-link {
            padding: 1rem 1.5rem;
            color: var(--dark-color);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            display: flex;
            align-items: center;
        }
        
        .nav-link i {
            margin-left: 8px;
        }
        
        .nav-link.active {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .nav-link:hover:not(.active) {
            background-color: var(--light-color);
        }
        
        /* Main Content */
        .main-content {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .page-title {
            margin-bottom: 1.5rem;
            color: var(--dark-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .btn {
            display: inline-block;
            padding: 0.5rem 1rem;
            background-color: var(--secondary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            text-decoration: none;
            font-size: 0.9rem;
            transition: var(--transition);
        }
        
        .btn:hover {
            background-color: #2980b9;
        }
        
        .btn-success {
            background-color: var(--success-color);
        }
        
        .btn-success:hover {
            background-color: #27ae60;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
        }
        
        .btn-danger:hover {
            background-color: #c0392b;
        }
        
        /* Alert Messages */
        .alert {
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        /* Category Tabs */
        .category-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 2rem;
            border-bottom: 1px solid #eee;
            padding-bottom: 1rem;
        }
        
        .category-tab {
            padding: 0.5rem 1rem;
            background-color: #f8f9fa;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            color: var(--dark-color);
            font-weight: 500;
            border: 1px solid #eee;
        }
        
        .category-tab:hover {
            background-color: #e9ecef;
        }
        
        .category-tab.active {
            background-color: var(--secondary-color);
            color: white;
            border-color: var(--secondary-color);
        }
        
        /* Products Grid */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 1.5rem;
        }
        
        .product-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: var(--transition);
            border: 1px solid #eee;
            position: relative;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .product-image {
            width: 100%;
            height: 180px;
            object-fit: cover;
            border-bottom: 1px solid #eee;
        }
        
        .product-info {
            padding: 1rem;
        }
        
        .product-name {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--dark-color);
        }
        
        .product-price {
            color: var(--accent-color);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .product-category {
            font-size: 0.85rem;
            color: #777;
            margin-bottom: 0.5rem;
        }
        
        .product-quantity {
            font-size: 0.85rem;
            color: #555;
            margin-bottom: 1rem;
        }
        
        .product-actions {
            display: flex;
            gap: 10px;
        }
        
        .product-actions .btn {
            flex: 1;
            text-align: center;
            font-size: 0.85rem;
        }
        
        /* Category Section */
        .category-section {
            margin-bottom: 3rem;
        }
        
        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--secondary-color);
        }
        
        .category-name {
            font-size: 1.25rem;
            color: var(--dark-color);
            font-weight: 600;
        }
        
        .category-count {
            background-color: var(--secondary-color);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 20px;
            font-size: 0.85rem;
        }
        
        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 2rem;
            color: #777;
        }
        
        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #ddd;
        }
        
        /* Footer */
        .footer {
            text-align: center;
            margin-top: 2rem;
            padding: 1rem;
            color: #777;
            font-size: 0.9rem;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .nav {
                flex-direction: column;
            }
            
            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            }
        }
        
        @media (max-width: 576px) {
            .products-grid {
                grid-template-columns: 1fr;
            }
            
            .page-title {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>إدارة المنتجات</h1>
            <div class="user-info">
                <span>مرحباً، <?php echo $_SESSION['admin_user_name']; ?></span>
                <a href="admin_login.php?logout=1" title="تسجيل الخروج"><i class="fas fa-sign-out-alt"></i></a>
            </div>
        </header>
        
        <!-- Navigation -->
        <nav class="nav">
            <a href="admin_dashboard.php" class="nav-link"><i class="fas fa-tachometer-alt"></i> الرئيسية</a>
            <a href="admin_categories.php" class="nav-link"><i class="fas fa-tags"></i> إدارة الأقسام</a>
            <a href="admin_products.php" class="nav-link active"><i class="fas fa-box"></i> إدارة المنتجات</a>
            <a href="admin_customer_orders.php" class="nav-link"><i class="fas fa-shopping-cart"></i> طلبيات العملاء</a>
            <a href="admin_settings.php" class="nav-link"><i class="fas fa-cog"></i> إعدادات الموقع</a>
            <a href="index.php" class="nav-link" target="_blank"><i class="fas fa-globe"></i> زيارة الموقع</a>
        </nav>
        
        <!-- Main Content -->
        <div class="main-content">
            <div class="page-title">
                <h2>إدارة المنتجات</h2>
                <a href="add_product.php" class="btn btn-success"><i class="fas fa-plus"></i> إضافة منتج جديد</a>
            </div>
            
            <?php if (isset($_GET['deleted'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> تم حذف المنتج بنجاح.
            </div>
            <?php endif; ?>
            
            <!-- Category Tabs -->
            <div class="category-tabs">
                <a href="admin_products.php" class="category-tab <?php echo empty($selectedCategory) ? 'active' : ''; ?>">
                    <i class="fas fa-cubes"></i> جميع المنتجات (<?php echo count($products); ?>)
                </a>
                
                <?php foreach ($categories as $category): ?>
                    <?php if ($category['active']): ?>
                        <?php 
                        $categoryName = $category['name'];
                        $categoryCount = isset($productsByCategory[$categoryName]) ? count($productsByCategory[$categoryName]) : 0;
                        ?>
                        <a href="admin_products.php?category=<?php echo urlencode($categoryName); ?>" 
                           class="category-tab <?php echo $selectedCategory === $categoryName ? 'active' : ''; ?>">
                            <?php echo $categoryName; ?> (<?php echo $categoryCount; ?>)
                        </a>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
            
            <?php if (empty($products)): ?>
            <div class="empty-state">
                <i class="fas fa-box-open"></i>
                <p>لا توجد منتجات مضافة حتى الآن.</p>
                <a href="add_product.php" class="btn btn-success">إضافة منتج جديد</a>
            </div>
            <?php else: ?>
                <?php if (empty($selectedCategory)): ?>
                    <!-- عرض المنتجات حسب الأقسام -->
                    <?php foreach ($productsByCategory as $categoryName => $categoryProducts): ?>
                        <div class="category-section">
                            <div class="category-header">
                                <h3 class="category-name"><?php echo $categoryName; ?></h3>
                                <span class="category-count"><?php echo count($categoryProducts); ?> منتج</span>
                            </div>
                            
                            <div class="products-grid">
                                <?php foreach ($categoryProducts as $product): ?>
                                    <div class="product-card">
                                        <img src="uploads/<?php echo $product['image']; ?>" alt="<?php echo $product['name']; ?>" class="product-image" onerror="this.src='https://via.placeholder.com/250x180?text=<?php echo urlencode($product['name']); ?>'">
                                        <div class="product-info">
                                            <h3 class="product-name"><?php echo $product['name']; ?></h3>
                                            <div class="product-price"><?php echo $product['price']; ?> ₪</div>
                                            <div class="product-category">
                                                <i class="fas fa-tag"></i> <?php echo $product['category']; ?>
                                            </div>
                                            <div class="product-quantity">
                                                <i class="fas fa-cubes"></i> المخزون: <?php echo $product['quantity']; ?> <?php echo $product['unit']; ?>
                                            </div>
                                            <div class="product-actions">
                                                <a href="edit.php?name=<?php echo urlencode($product['name']); ?>" class="btn">
                                                    <i class="fas fa-edit"></i> تعديل
                                                </a>
                                                <a href="admin_products.php?delete=<?php echo urlencode($product['name']); ?>" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المنتج؟')">
                                                    <i class="fas fa-trash"></i> حذف
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- عرض منتجات القسم المحدد -->
                    <?php 
                    $categoryProducts = isset($productsByCategory[$selectedCategory]) ? $productsByCategory[$selectedCategory] : [];
                    ?>
                    <div class="category-section">
                        <div class="category-header">
                            <h3 class="category-name"><?php echo $selectedCategory; ?></h3>
                            <span class="category-count"><?php echo count($categoryProducts); ?> منتج</span>
                        </div>
                        
                        <?php if (empty($categoryProducts)): ?>
                            <div class="empty-state">
                                <i class="fas fa-box-open"></i>
                                <p>لا توجد منتجات في هذا القسم.</p>
                                <a href="add_product.php" class="btn btn-success">إضافة منتج جديد</a>
                            </div>
                        <?php else: ?>
                            <div class="products-grid">
                                <?php foreach ($categoryProducts as $product): ?>
                                    <div class="product-card">
                                        <img src="uploads/<?php echo $product['image']; ?>" alt="<?php echo $product['name']; ?>" class="product-image" onerror="this.src='https://via.placeholder.com/250x180?text=<?php echo urlencode($product['name']); ?>'">
                                        <div class="product-info">
                                            <h3 class="product-name"><?php echo $product['name']; ?></h3>
                                            <div class="product-price"><?php echo $product['price']; ?> ₪</div>
                                            <div class="product-category">
                                                <i class="fas fa-tag"></i> <?php echo $product['category']; ?>
                                            </div>
                                            <div class="product-quantity">
                                                <i class="fas fa-cubes"></i> المخزون: <?php echo $product['quantity']; ?> <?php echo $product['unit']; ?>
                                            </div>
                                            <div class="product-actions">
                                                <a href="edit.php?name=<?php echo urlencode($product['name']); ?>" class="btn">
                                                    <i class="fas fa-edit"></i> تعديل
                                                </a>
                                                <a href="admin_products.php?delete=<?php echo urlencode($product['name']); ?>" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المنتج؟')">
                                                    <i class="fas fa-trash"></i> حذف
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
        
        <!-- Footer -->
        <footer class="footer">
            <p>نظام إدارة المنتجات &copy; <?php echo date('Y'); ?> - جميع الحقوق محفوظة</p>
        </footer>
    </div>
</body>
</html>