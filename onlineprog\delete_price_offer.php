<?php
// تمكين عرض الأخطاء للتصحيح
error_reporting(E_ALL);
ini_set('display_errors', 1);

// التحقق من وجود معرف العرض
if (isset($_GET['id'])) {
    $offerId = $_GET['id'];
    $offersFile = 'price_offers_data.json';
    
    // التحقق من وجود ملف عروض الأسعار
    if (file_exists($offersFile) && filesize($offersFile) > 0) {
        // قراءة البيانات الحالية
        $offersData = json_decode(file_get_contents($offersFile), true);
        
        // البحث عن العرض وحذفه
        $found = false;
        foreach ($offersData as $key => $offer) {
            if ($offer['offer_id'] == $offerId) {
                unset($offersData[$key]);
                $found = true;
                break;
            }
        }
        
        // إعادة ترتيب المصفوفة
        $offersData = array_values($offersData);
        
        // حفظ البيانات المحدثة
        if ($found) {
            file_put_contents($offersFile, json_encode($offersData, JSON_PRETTY_PRINT));
        }
    }
    
    // إعادة التوجيه إلى صفحة عروض الأسعار المحفوظة
    header('Location: saved_price_offers.php');
    exit;
} else {
    // إعادة التوجيه إلى الصفحة الرئيسية إذا لم يتم تحديد معرف العرض
    header('Location: index.php');
    exit;
}
?>