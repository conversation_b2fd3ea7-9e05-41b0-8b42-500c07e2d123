<?php
require_once 'admin_auth.php';

// التحقق من تسجيل الدخول
requireLogin();

// تحميل بيانات الأخبار
$newsFile = 'news_data.json';
$news = [];

// إنشاء ملف الأخبار إذا لم يكن موجوداً
if (!file_exists($newsFile)) {
    // أخبار افتراضية
    $defaultNews = [
        [
            'id' => 1,
            'title' => 'عروض خاصة على القرطاسية المدرسية',
            'date' => date('Y-m-d'),
            'active' => true
        ]
    ];
    
    // حفظ الأخبار الافتراضية في الملف
    file_put_contents($newsFile, json_encode($defaultNews, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    $news = $defaultNews;
} else {
    // تحميل الأخبار من الملف
    $news = json_decode(file_get_contents($newsFile), true);
}

// إضافة خبر جديد
if (isset($_POST['add_news'])) {
    $newId = 1;
    if (!empty($news)) {
        $ids = array_column($news, 'id');
        $newId = max($ids) + 1;
    }
    
    $newNews = [
        'id' => $newId,
        'title' => $_POST['news_title'],
        'date' => date('Y-m-d'),
        'active' => true
    ];
    
    $news[] = $newNews;
    file_put_contents($newsFile, json_encode($news, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    header('Location: admin_news.php?added=1');
    exit;
}

// حذف خبر
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $deleteId = intval($_GET['delete']);
    foreach ($news as $key => $item) {
        if ($item['id'] === $deleteId) {
            unset($news[$key]);
            break;
        }
    }
    
    $news = array_values($news); // إعادة ترتيب المصفوفة
    file_put_contents($newsFile, json_encode($news, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    header('Location: admin_news.php?deleted=1');
    exit;
}

// تغيير حالة الخبر (نشط/غير نشط)
if (isset($_GET['toggle']) && is_numeric($_GET['toggle'])) {
    $toggleId = intval($_GET['toggle']);
    foreach ($news as $key => $item) {
        if ($item['id'] === $toggleId) {
            $news[$key]['active'] = !$news[$key]['active'];
            break;
        }
    }
    
    file_put_contents($newsFile, json_encode($news, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    header('Location: admin_news.php?updated=1');
    exit;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأخبار</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2ecc71;
            --dark-color: #2c3e50;
            --light-color: #ecf0f1;
            --danger-color: #e74c3c;
            --success-color: #27ae60;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .user-info a {
            color: var(--dark-color);
            text-decoration: none;
        }
        
        .nav {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            background-color: white;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .nav-link {
            padding: 8px 12px;
            color: var(--dark-color);
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .nav-link:hover {
            background-color: var(--light-color);
        }
        
        .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .page-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background-color: #2980b9;
        }
        
        .btn-success {
            background-color: var(--success-color);
        }
        
        .btn-success:hover {
            background-color: #219653;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
        }
        
        .btn-danger:hover {
            background-color: #c0392b;
        }
        
        .alert {
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 12px 15px;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background-color: var(--primary-color);
            color: white;
        }
        
        tr:hover {
            background-color: #f5f5f5;
        }
        
        .actions {
            display: flex;
            gap: 5px;
        }
        
        .footer {
            text-align: center;
            margin-top: 2rem;
            padding: 1rem;
            color: #777;
            font-size: 0.9rem;
        }
        
        .status-active {
            color: var(--success-color);
        }
        
        .status-inactive {
            color: var(--danger-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>إدارة الأخبار</h1>
            <div class="user-info">
                <span>مرحباً، <?php echo $_SESSION['admin_user_name']; ?></span>
                <a href="admin_login.php?logout=1" title="تسجيل الخروج"><i class="fas fa-sign-out-alt"></i></a>
            </div>
        </header>
        
        <!-- Navigation -->
        <nav class="nav">
            <a href="admin_dashboard.php" class="nav-link"><i class="fas fa-tachometer-alt"></i> الرئيسية</a>
            <a href="admin_categories.php" class="nav-link"><i class="fas fa-tags"></i> إدارة الأقسام</a>
            <a href="index.php" class="nav-link"><i class="fas fa-box"></i> إدارة المنتجات</a>
            <a href="admin_customer_orders.php" class="nav-link"><i class="fas fa-shopping-cart"></i> طلبيات العملاء</a>
            <a href="admin_news.php" class="nav-link active"><i class="fas fa-newspaper"></i> إدارة الأخبار</a>
            <a href="admin_settings.php" class="nav-link"><i class="fas fa-cog"></i> إعدادات الموقع</a>
            <a href="customer.php" class="nav-link" target="_blank"><i class="fas fa-globe"></i> زيارة الموقع</a>
        </nav>
        
        <!-- Main Content -->
        <div class="main-content">
            <div class="page-title">
                <h2>إدارة الأخبار</h2>
                <button type="button" class="btn btn-success" data-toggle="modal" data-target="#addNewsModal">
                    <i class="fas fa-plus"></i> إضافة خبر جديد
                </button>
            </div>
            
            <?php if (isset($_GET['added'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> تم إضافة الخبر بنجاح.
            </div>
            <?php endif; ?>
            
            <?php if (isset($_GET['deleted'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> تم حذف الخبر بنجاح.
            </div>
            <?php endif; ?>
            
            <?php if (isset($_GET['updated'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> تم تحديث حالة الخبر بنجاح.
            </div>
            <?php endif; ?>
            
            <!-- جدول الأخبار -->
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>عنوان الخبر</th>
                        <th>التاريخ</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($news)): ?>
                    <tr>
                        <td colspan="5" style="text-align: center;">لا توجد أخبار حالياً</td>
                    </tr>
                    <?php else: ?>
                    <?php foreach ($news as $index => $item): ?>
                    <tr>
                        <td><?php echo $index + 1; ?></td>
                        <td><?php echo $item['title']; ?></td>
                        <td><?php echo $item['date']; ?></td>
                        <td>
                            <?php if ($item['active']): ?>
                            <span class="status-active"><i class="fas fa-check-circle"></i> نشط</span>
                            <?php else: ?>
                            <span class="status-inactive"><i class="fas fa-times-circle"></i> غير نشط</span>
                            <?php endif; ?>
                        </td>
                        <td class="actions">
                            <a href="admin_news.php?toggle=<?php echo $item['id']; ?>" class="btn btn-success" title="تغيير الحالة">
                                <i class="fas fa-sync-alt"></i>
                            </a>
                            <a href="admin_news.php?delete=<?php echo $item['id']; ?>" class="btn btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا الخبر؟')">
                                <i class="fas fa-trash"></i>
                            </a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
            
            <!-- نموذج إضافة خبر جديد -->
            <div id="addNewsForm" style="background-color: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                <h3>إضافة خبر جديد</h3>
                <form method="post" action="">
                    <div class="form-group">
                        <label for="news_title">عنوان الخبر:</label>
                        <input type="text" id="news_title" name="news_title" class="form-control" required>
                    </div>
                    
                    <button type="submit" name="add_news" class="btn btn-success"><i class="fas fa-save"></i> حفظ الخبر</button>
                </form>
            </div>
        </div>
        
        <div class="footer">
            <p>جميع الحقوق محفوظة &copy; <?php echo date('Y'); ?> - نظام إدارة المنتجات</p>
        </div>
    </div>
</body>
</html>