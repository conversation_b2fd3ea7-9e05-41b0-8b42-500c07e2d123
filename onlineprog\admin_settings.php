<?php
require_once 'admin_auth.php';

// التحقق من تسجيل الدخول
requireLogin();

// تحميل إعدادات الموقع
$settingsFile = 'site_settings.json';
$settings = [];

// إنشاء ملف الإعدادات إذا لم يكن موجوداً
if (!file_exists($settingsFile)) {
    // إعدادات افتراضية
    $defaultSettings = [
        'slider' => [
            'autoplay' => true,
            'delay' => 3000,
            'loop' => true,
            'effect' => 'slide',
            'show_navigation' => true,
            'show_pagination' => true,
            'items_count' => 6
        ],
        'site' => [
            'title' => 'متجر القرطاسية - الصفحة الرئيسية',
            'footer_text' => 'جميع الحقوق محفوظة - متجر القرطاسية'
        ],
        'categories' => [
            'icon_shape' => 'circle',
            'border_radius' => 50,
            'hover_effect' => true
        ]
    ];
    
    // حفظ الإعدادات الافتراضية في الملف
    file_put_contents($settingsFile, json_encode($defaultSettings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    $settings = $defaultSettings;
} else {
    // تحميل الإعدادات من الملف
    $settings = json_decode(file_get_contents($settingsFile), true);
    
    // إضافة إعدادات الأقسام إذا لم تكن موجودة
    if (!isset($settings['categories'])) {
        $settings['categories'] = [
            'icon_shape' => 'circle',
            'border_radius' => 50,
            'hover_effect' => true
        ];
        file_put_contents($settingsFile, json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }
}

// معالجة تحديث إعدادات السلايدر
if (isset($_POST['update_slider_settings'])) {
    // تحديث إعدادات السلايدر
    $settings['slider'] = [
        'enabled' => isset($_POST['slider_enabled']), // جديد: تفعيل/تعطيل السلايدر
        'autoplay' => isset($_POST['autoplay']),
        'delay' => intval($_POST['delay']),
        'loop' => isset($_POST['loop']),
        'effect' => $_POST['effect'],
        'show_navigation' => isset($_POST['show_navigation']),
        'show_pagination' => isset($_POST['show_pagination']),
        'show_content' => isset($_POST['show_content']),
        'items_count' => intval($_POST['items_count'])
    ];
    
    // حفظ الإعدادات المحدثة
    file_put_contents($settingsFile, json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    header('Location: admin_settings.php?updated=1');
    exit;
}

// معالجة تحديث إعدادات الموقع العامة
if (isset($_POST['update_site_settings'])) {
    // تحديث إعدادات الموقع
    $settings['site'] = [
        'title' => $_POST['site_title'],
        'footer_text' => $_POST['footer_text'],
        'logo' => isset($settings['site']['logo']) ? $settings['site']['logo'] : 'default-logo.png'
    ];
    
    // معالجة تحميل الشعار الجديد
    if (isset($_FILES['site_logo']) && $_FILES['site_logo']['error'] === 0) {
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $fileType = $_FILES['site_logo']['type'];
        
        if (in_array($fileType, $allowedTypes)) {
            $fileName = 'site-logo-' . time() . '-' . $_FILES['site_logo']['name'];
            $uploadPath = 'uploads/' . $fileName;
            
            if (move_uploaded_file($_FILES['site_logo']['tmp_name'], $uploadPath)) {
                $settings['site']['logo'] = $fileName;
            }
        }
    }
    
    // حفظ الإعدادات المحدثة
    file_put_contents($settingsFile, json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    header('Location: admin_settings.php?updated=1');
    exit;
}

// معالجة تحديث إعدادات أيقونات الأقسام
if (isset($_POST['update_categories_settings'])) {
    // تحديث إعدادات أيقونات الأقسام
    $settings['categories'] = [
        'icon_shape' => $_POST['icon_shape'],
        'border_radius' => intval($_POST['border_radius']),
        'hover_effect' => isset($_POST['hover_effect'])
    ];
    
    // حفظ الإعدادات المحدثة
    file_put_contents($settingsFile, json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    header('Location: admin_settings.php?updated=1');
    exit;
}

// معالجة تحديث إعدادات الشريط الإخباري
if (isset($_POST['update_news_ticker_settings'])) {
    // تحديث إعدادات الشريط الإخباري
    $settings['news_ticker'] = [
        'enabled' => isset($_POST['news_ticker_enabled']),
        'speed' => intval($_POST['news_ticker_speed']),
        'direction' => $_POST['news_ticker_direction'],
        'background_color' => $_POST['news_ticker_bg_color'],
        'text_color' => $_POST['news_ticker_text_color']
    ];
    
    // حفظ الإعدادات المحدثة
    file_put_contents($settingsFile, json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    header('Location: admin_settings.php?updated=1');
    exit;
}

// معالجة تحديث إعدادات مربع البحث
if (isset($_POST['update_search_box_settings'])) {
    // تحديث إعدادات مربع البحث
    $settings['search_box'] = [
        'enabled' => isset($_POST['search_box_enabled']),
        'placeholder_text' => $_POST['placeholder_text'],
        'animation_enabled' => isset($_POST['animation_enabled']),
        'animation_speed' => intval($_POST['animation_speed']),
        'background_color' => $_POST['background_color'],
        'text_color' => $_POST['text_color'],
        'border_color' => $_POST['border_color'],
        'button_color' => $_POST['button_color'],
        'button_text_color' => $_POST['button_text_color']
    ];
    
    // حفظ الإعدادات المحدثة
    file_put_contents($settingsFile, json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    header('Location: admin_settings.php?updated=1');
    exit;
}

// تحميل بيانات الأقسام لإظهارها في قائمة منسدلة
$categoriesFile = 'categories_data.json';
$categoriesData = [];
if (file_exists($categoriesFile)) {
    $categoriesData = json_decode(file_get_contents($categoriesFile), true);
}

// معالجة تحديث إعدادات السلايدر الخاص بالمنتجات أو الإعلانات
if (isset($_POST['update_custom_sliders'])) {
    $settings['products_slider_category'] = $_POST['products_slider_category'];
    $settings['ads_slider_category'] = $_POST['ads_slider_category'];
    $settings['custom_sliders_enabled'] = isset($_POST['custom_sliders_enabled']);
    $settings['custom_sliders_show_on_customer'] = isset($_POST['custom_sliders_show_on_customer']);
    $settings['hide_products_slider_settings'] = isset($_POST['hide_products_slider_settings']);
    // جديد: زر إخفاء حقل اختيار قسم الإعلانات
    $settings['hide_ads_slider_category_field'] = isset($_POST['hide_ads_slider_category_field']);
    file_put_contents($settingsFile, json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    header('Location: admin_settings.php?updated=1');
    exit;
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الموقع - نظام إدارة المنتجات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--primary-color);
            color: white;
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
            box-shadow: var(--box-shadow);
        }
        
        .header h1 {
            font-size: 1.8rem;
            margin: 0;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .user-info span {
            margin-left: 1rem;
        }
        
        .user-info a {
            color: white;
            text-decoration: none;
            font-size: 1.2rem;
        }
        
        /* Navigation */
        .nav {
            display: flex;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .nav-link {
            padding: 1rem 1.5rem;
            color: var(--dark-color);
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: var(--transition);
            font-weight: 500;
        }
        
        .nav-link i {
            margin-left: 8px;
        }
        
        .nav-link.active {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .nav-link:hover:not(.active) {
            background-color: #f0f0f0;
        }
        
        /* Main Content */
        .main-content {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .page-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }
        
        .page-title h2 {
            font-size: 1.5rem;
            color: var(--primary-color);
        }
        
        /* Forms */
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        
        .form-control {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: var(--transition);
        }
        
        .form-control:focus {
            border-color: var(--secondary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }
        
        .form-check {
            display: flex;
            align-items: center;
            margin-bottom: 0.8rem;
        }
        
        .form-check input {
            margin-left: 0.5rem;
        }
        
        /* Buttons */
        .btn {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            background-color: var(--secondary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            text-decoration: none;
            transition: var(--transition);
        }
        
        .btn:hover {
            background-color: #2980b9;
        }
        
        .btn-success {
            background-color: var(--success-color);
        }
        
        .btn-success:hover {
            background-color: #27ae60;
        }
        
        /* Alerts */
        .alert {
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
        }
        
        /* Settings Sections */
        .settings-section {
            margin-bottom: 2rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #eee;
        }
        
        .settings-section:last-child {
            border-bottom: none;
            padding-bottom: 0;
            margin-bottom: 0;
        }
        
        .settings-section h3 {
            margin-bottom: 1.5rem;
            color: var(--primary-color);
        }
        
        /* Footer */
        .footer {
            text-align: center;
            margin-top: 2rem;
            padding: 1rem;
            color: #777;
            font-size: 0.9rem;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .nav {
                flex-direction: column;
            }
            
            .page-title {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .page-title h2 {
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>إعدادات الموقع</h1>
            <div class="user-info">
                <span>مرحباً، <?php echo $_SESSION['admin_user_name']; ?></span>
                <a href="admin_login.php?logout=1" title="تسجيل الخروج"><i class="fas fa-sign-out-alt"></i></a>
            </div>
        </header>
        
        <!-- Navigation -->
        <nav class="nav">
            <a href="admin_dashboard.php" class="nav-link"><i class="fas fa-tachometer-alt"></i> الرئيسية</a>
            <a href="admin_categories.php" class="nav-link"><i class="fas fa-tags"></i> إدارة الأقسام</a>
            <a href="index.php" class="nav-link"><i class="fas fa-box"></i> إدارة المنتجات</a>
            <a href="admin_customer_orders.php" class="nav-link"><i class="fas fa-shopping-cart"></i> طلبيات العملاء</a>
            <a href="admin_news.php" class="nav-link"><i class="fas fa-newspaper"></i> إدارة الأخبار</a>
            <a href="admin_settings.php" class="nav-link active"><i class="fas fa-cog"></i> إعدادات الموقع</a>
            <a href="customer.php" class="nav-link" target="_blank"><i class="fas fa-globe"></i> زيارة الموقع</a>
        </nav>
        
        <!-- Main Content -->
        <div class="main-content">
            <div class="page-title">
                <h2>إعدادات الموقع</h2>
            </div>
            
            <?php if (isset($_GET['updated'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> تم تحديث الإعدادات بنجاح.
            </div>
            <?php endif; ?>
            
            <!-- إعدادات السلايدر -->
            <div class="settings-section">
                <h3><i class="fas fa-sliders-h"></i> إعدادات السلايد شو</h3>
                <form method="post" action="">
                    <div class="form-check">
                        <input type="checkbox" id="slider_enabled" name="slider_enabled" <?php echo (isset($settings['slider']['enabled']) && $settings['slider']['enabled']) ? 'checked' : ''; ?>>
                        <label for="slider_enabled">تفعيل السلايدر الرئيسي</label>
                    </div>
                    <div class="form-group">
                        <label for="items_count">عدد العناصر في السلايدر:</label>
                        <input type="number" id="items_count" name="items_count" class="form-control" value="<?php echo $settings['slider']['items_count']; ?>" min="1" max="20">
                    </div>
                    
                    <div class="form-group">
                        <label for="delay">مدة التأخير بين الشرائح (بالمللي ثانية):</label>
                        <input type="number" id="delay" name="delay" class="form-control" value="<?php echo $settings['slider']['delay']; ?>" min="1000" max="10000" step="500">
                    </div>
                    
                    <div class="form-group">
                        <label for="effect">تأثير الانتقال:</label>
                        <select id="effect" name="effect" class="form-control">
                            <option value="slide" <?php echo $settings['slider']['effect'] === 'slide' ? 'selected' : ''; ?>>انزلاق</option>
                            <option value="fade" <?php echo $settings['slider']['effect'] === 'fade' ? 'selected' : ''; ?>>تلاشي</option>
                            <option value="cube" <?php echo $settings['slider']['effect'] === 'cube' ? 'selected' : ''; ?>>مكعب</option>
                            <option value="coverflow" <?php echo $settings['slider']['effect'] === 'coverflow' ? 'selected' : ''; ?>>تغطية</option>
                            <option value="flip" <?php echo $settings['slider']['effect'] === 'flip' ? 'selected' : ''; ?>>قلب</option>
                        </select>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" id="autoplay" name="autoplay" <?php echo $settings['slider']['autoplay'] ? 'checked' : ''; ?>>
                        <label for="autoplay">تشغيل تلقائي</label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" id="loop" name="loop" <?php echo $settings['slider']['loop'] ? 'checked' : ''; ?>>
                        <label for="loop">تكرار العرض</label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" id="show_navigation" name="show_navigation" <?php echo $settings['slider']['show_navigation'] ? 'checked' : ''; ?>>
                        <label for="show_navigation">إظهار أزرار التنقل</label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" id="show_pagination" name="show_pagination" <?php echo $settings['slider']['show_pagination'] ? 'checked' : ''; ?>>
                        <label for="show_pagination">إظهار نقاط الترقيم</label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" id="show_content" name="show_content" <?php echo isset($settings['slider']['show_content']) && $settings['slider']['show_content'] ? 'checked' : ''; ?>>
                        <label for="show_content">إظهار محتوى الشريحة (العنوان والسعر وزر التسوق)</label>
                    </div>
                    
                    <button type="submit" name="update_slider_settings" class="btn btn-success"><i class="fas fa-save"></i> حفظ إعدادات السلايدر</button>
                </form>
            </div>
            
            <!-- إعدادات الموقع العامة -->
            <div class="settings-section">
                <h3><i class="fas fa-globe"></i> إعدادات الموقع العامة</h3>
                <form method="post" action="" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="site_title">عنوان الموقع:</label>
                        <input type="text" id="site_title" name="site_title" class="form-control" value="<?php echo $settings['site']['title']; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="footer_text">نص التذييل:</label>
                        <input type="text" id="footer_text" name="footer_text" class="form-control" value="<?php echo $settings['site']['footer_text']; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="site_logo">شعار الموقع:</label>
                        <?php if (isset($settings['site']['logo']) && !empty($settings['site']['logo'])): ?>
                            <div class="current-logo">
                                <img src="uploads/<?php echo $settings['site']['logo']; ?>" alt="الشعار الحالي" style="max-height: 100px; margin-bottom: 10px;">
                                <p>الشعار الحالي</p>
                            </div>
                        <?php endif; ?>
                        <input type="file" id="site_logo" name="site_logo" class="form-control" accept="image/*">
                        <small class="form-text text-muted">يفضل استخدام صورة بخلفية شفافة بأبعاد مناسبة (مثل 200×50 بكسل)</small>
                    </div>
                    
                    <button type="submit" name="update_site_settings" class="btn btn-success"><i class="fas fa-save"></i> حفظ إعدادات الموقع</button>
                </form>
            </div>
            <!-- إعدادات أيقونات الأقسام -->
            <div class="settings-section">
                <h3><i class="fas fa-th-large"></i> إعدادات أيقونات الأقسام</h3>
                <form method="post" action="">
                    <div class="form-group">
                        <label for="icon_shape">شكل الأيقونات:</label>
                        <select id="icon_shape" name="icon_shape" class="form-control">
                            <option value="circle" <?php echo isset($settings['categories']['icon_shape']) && $settings['categories']['icon_shape'] === 'circle' ? 'selected' : ''; ?>>دائري</option>
                            <option value="rounded" <?php echo isset($settings['categories']['icon_shape']) && $settings['categories']['icon_shape'] === 'rounded' ? 'selected' : ''; ?>>مستطيل بحواف منحنية</option>
                            <option value="square" <?php echo isset($settings['categories']['icon_shape']) && $settings['categories']['icon_shape'] === 'square' ? 'selected' : ''; ?>>مربع</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="border_radius">نسبة استدارة الحواف (0-50):</label>
                        <input type="range" id="border_radius" name="border_radius" class="form-control" value="<?php echo isset($settings['categories']['border_radius']) ? $settings['categories']['border_radius'] : 50; ?>" min="0" max="50" step="1">
                        <output for="border_radius" id="border_radius_value"><?php echo isset($settings['categories']['border_radius']) ? $settings['categories']['border_radius'] : 50; ?></output>%
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" id="hover_effect" name="hover_effect" <?php echo isset($settings['categories']['hover_effect']) && $settings['categories']['hover_effect'] ? 'checked' : ''; ?>>
                        <label for="hover_effect">تأثير التكبير عند المرور</label>
                    </div>
                    
                    <button type="submit" name="update_categories_settings" class="btn btn-success"><i class="fas fa-save"></i> حفظ إعدادات الأيقونات</button>
                </form>
            </div>
            
            <!-- إعدادات الشريط الإخباري -->
            <div class="settings-section">
                <h3><i class="fas fa-newspaper"></i> إعدادات الشريط الإخباري</h3>
                <form method="post" action="">
                    <div class="form-check">
                        <input type="checkbox" id="news_ticker_enabled" name="news_ticker_enabled" <?php echo isset($settings['news_ticker']['enabled']) && $settings['news_ticker']['enabled'] ? 'checked' : ''; ?>>
                        <label for="news_ticker_enabled">تفعيل الشريط الإخباري</label>
                    </div>
                    
                    <div class="form-group">
                        <label for="news_ticker_speed">سرعة الشريط (1-100):</label>
                        <input type="range" id="news_ticker_speed" name="news_ticker_speed" class="form-control" value="<?php echo isset($settings['news_ticker']['speed']) ? $settings['news_ticker']['speed'] : 30; ?>" min="1" max="100" step="1">
                        <output for="news_ticker_speed" id="news_ticker_speed_value"><?php echo isset($settings['news_ticker']['speed']) ? $settings['news_ticker']['speed'] : 30; ?></output>
                    </div>
                    
                    <div class="form-group">
                        <label for="news_ticker_direction">اتجاه الشريط:</label>
                        <select id="news_ticker_direction" name="news_ticker_direction" class="form-control">
                            <option value="right" <?php echo isset($settings['news_ticker']['direction']) && $settings['news_ticker']['direction'] === 'right' ? 'selected' : ''; ?>>من اليسار إلى اليمين</option>
                            <option value="left" <?php echo isset($settings['news_ticker']['direction']) && $settings['news_ticker']['direction'] === 'left' ? 'selected' : ''; ?>>من اليمين إلى اليسار</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="news_ticker_bg_color">لون خلفية الشريط:</label>
                        <input type="color" id="news_ticker_bg_color" name="news_ticker_bg_color" class="form-control" value="<?php echo isset($settings['news_ticker']['background_color']) ? $settings['news_ticker']['background_color'] : '#3498db'; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="news_ticker_text_color">لون نص الشريط:</label>
                        <input type="color" id="news_ticker_text_color" name="news_ticker_text_color" class="form-control" value="<?php echo isset($settings['news_ticker']['text_color']) ? $settings['news_ticker']['text_color'] : '#ffffff'; ?>">
                    </div>
                    
                    <button type="submit" name="update_news_ticker_settings" class="btn btn-success"><i class="fas fa-save"></i> حفظ إعدادات الشريط الإخباري</button>
                </form>
            </div>
            
            <!-- إعدادات السلايدر الخاص بالمنتجات والإعلانات -->
            <div class="settings-section">
                <h3><i class="fas fa-images"></i> إعدادات السلايدر المخصص</h3>
                <form method="post" action="">
                    <div class="form-check">
                        <input type="checkbox" id="custom_sliders_enabled" name="custom_sliders_enabled" <?php echo (isset($settings['custom_sliders_enabled']) && $settings['custom_sliders_enabled']) ? 'checked' : ''; ?>>
                        <label for="custom_sliders_enabled">إظهار إعدادات السلايدر المخصص في لوحة التحكم</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" id="custom_sliders_show_on_customer" name="custom_sliders_show_on_customer" <?php echo (isset($settings['custom_sliders_show_on_customer']) && $settings['custom_sliders_show_on_customer']) ? 'checked' : ''; ?>>
                        <label for="custom_sliders_show_on_customer">إظهار السلايدر المخصص في صفحة العميل</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" id="hide_products_slider_settings" name="hide_products_slider_settings" <?php echo (isset($settings['hide_products_slider_settings']) && $settings['hide_products_slider_settings']) ? 'checked' : ''; ?>>
                        <label for="hide_products_slider_settings">إخفاء السلايدر المخصص من صفحة العميل</label>
                    </div>
                    <!-- زر جديد لإخفاء حقل اختيار قسم الإعلانات -->
                    <div class="form-check">
                        <input type="checkbox" id="hide_ads_slider_category_field" name="hide_ads_slider_category_field" <?php echo (isset($settings['hide_ads_slider_category_field']) && $settings['hide_ads_slider_category_field']) ? 'checked' : ''; ?>>
                        <label for="hide_ads_slider_category_field">إخفاء حقل اختيار قسم الإعلانات</label>
                    </div>
                    <div id="custom-sliders-fields" style="<?php echo (isset($settings['custom_sliders_enabled']) && $settings['custom_sliders_enabled']) ? '' : 'display:none;'; ?>">
                        <div class="form-group">
                            <label for="products_slider_category">اختر القسم لعرض منتجاته في سلايدر المنتجات:</label>
                            <select id="products_slider_category" name="products_slider_category" class="form-control">
                                <?php foreach ($categoriesData as $cat): ?>
                                    <?php if (isset($cat['name'])): ?>
                                        <option value="<?php echo htmlspecialchars($cat['name']); ?>" <?php echo (isset($settings['products_slider_category']) && $settings['products_slider_category'] === $cat['name']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($cat['name']); ?>
                                        </option>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <!-- حقل اختيار قسم الإعلانات يظهر فقط إذا لم يكن الإخفاء مفعلاً -->
                        <?php if (empty($settings['hide_ads_slider_category_field'])): ?>
                        <div class="form-group">
                            <label for="ads_slider_category">اختر القسم لعرض صوره في سلايدر الإعلانات:</label>
                            <select id="ads_slider_category" name="ads_slider_category" class="form-control">
                                <?php foreach ($categoriesData as $cat): ?>
                                    <?php if (isset($cat['name'])): ?>
                                        <option value="<?php echo htmlspecialchars($cat['name']); ?>" <?php echo (isset($settings['ads_slider_category']) && $settings['ads_slider_category'] === $cat['name']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($cat['name']); ?>
                                        </option>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <?php endif; ?>
                    </div>
                    <button type="submit" name="update_custom_sliders" class="btn btn-success"><i class="fas fa-save"></i> حفظ إعدادات السلايدر المخصص</button>
                </form>
            </div>
        <!-- إعدادات مربع البحث -->
        <div class="settings-section">
            <h3><i class="fas fa-search"></i> إعدادات مربع البحث</h3>
            <form method="post" action="">
                <div class="form-check">
                    <input type="checkbox" id="search_box_enabled" name="search_box_enabled" <?php echo isset($settings['search_box']['enabled']) && $settings['search_box']['enabled'] ? 'checked' : ''; ?>>
                    <label for="search_box_enabled">تفعيل مربع البحث</label>
                </div>
                
                <div class="form-group">
                    <label for="placeholder_text">نص العنصر النائب:</label>
                    <input type="text" id="placeholder_text" name="placeholder_text" class="form-control" value="<?php echo isset($settings['search_box']['placeholder_text']) ? $settings['search_box']['placeholder_text'] : 'ابحث عن منتجات...'; ?>">
                </div>
                
                <div class="form-check">
                    <input type="checkbox" id="animation_enabled" name="animation_enabled" <?php echo isset($settings['search_box']['animation_enabled']) && $settings['search_box']['animation_enabled'] ? 'checked' : ''; ?>>
                    <label for="animation_enabled">تفعيل النص المتحرك</label>
                </div>
                
                <div class="form-group">
                    <label for="animation_speed">سرعة الحركة (بالثواني):</label>
                    <input type="number" id="animation_speed" name="animation_speed" class="form-control" value="<?php echo isset($settings['search_box']['animation_speed']) ? $settings['search_box']['animation_speed'] : 3; ?>" min="1" max="10" step="0.5">
                </div>
                
                <div class="form-group">
                    <label for="background_color">لون خلفية مربع البحث:</label>
                    <input type="color" id="background_color" name="background_color" class="form-control" value="<?php echo isset($settings['search_box']['background_color']) ? $settings['search_box']['background_color'] : '#f8f9fa'; ?>">
                </div>
                
                <div class="form-group">
                    <label for="text_color">لون النص:</label>
                    <input type="color" id="text_color" name="text_color" class="form-control" value="<?php echo isset($settings['search_box']['text_color']) ? $settings['search_box']['text_color'] : '#333333'; ?>">
                </div>
                
                <div class="form-group">
                    <label for="border_color">لون الحدود:</label>
                    <input type="color" id="border_color" name="border_color" class="form-control" value="<?php echo isset($settings['search_box']['border_color']) ? $settings['search_box']['border_color'] : '#337fd1'; ?>">
                </div>
                
                <div class="form-group">
                    <label for="button_color">لون زر البحث:</label>
                    <input type="color" id="button_color" name="button_color" class="form-control" value="<?php echo isset($settings['search_box']['button_color']) ? $settings['search_box']['button_color'] : '#337fd1'; ?>">
                </div>
                
                <div class="form-group">
                    <label for="button_text_color">لون نص زر البحث:</label>
                    <input type="color" id="button_text_color" name="button_text_color" class="form-control" value="<?php echo isset($settings['search_box']['button_text_color']) ? $settings['search_box']['button_text_color'] : '#ffffff'; ?>">
                </div>
                
                <button type="submit" name="update_search_box_settings" class="btn btn-success"><i class="fas fa-save"></i> حفظ إعدادات مربع البحث</button>
            </form>
        </div>
    </div>
    <script>
        // عرض قيمة شريط سرعة الشريط الإخباري
        const newsTickerSpeedSlider = document.getElementById('news_ticker_speed');
        const newsTickerSpeedValue = document.getElementById('news_ticker_speed_value');
        
        if (newsTickerSpeedSlider && newsTickerSpeedValue) {
            newsTickerSpeedSlider.addEventListener('input', function() {
                newsTickerSpeedValue.textContent = this.value;
            });
        }
        
        // إظهار/إخفاء حقول إعدادات السلايدر المخصص حسب حالة التفعيل
        document.addEventListener('DOMContentLoaded', function() {
            var customSlidersCheckbox = document.getElementById('custom_sliders_enabled');
            var customSlidersFields = document.getElementById('custom-sliders-fields');
            if (customSlidersCheckbox && customSlidersFields) {
                customSlidersCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        customSlidersFields.style.display = '';
                    } else {
                        customSlidersFields.style.display = 'none';
                    }
                });
            }
        });
    </script>
    
     <div class="footer">
            <p>جميع الحقوق محفوظة &copy; <?php echo date('Y'); ?> - نظام إدارة المنتجات</p>
        </div>
</body>
</html>