<?php
session_start();

// التحقق مما إذا كان المستخدم مسجل الدخول بالفعل
if (isset($_SESSION['customer_id'])) {
    // إعادة التوجيه إلى الصفحة الرئيسية أو صفحة الحساب
    header('Location: customer_products.php');
    exit;
}

$error = '';
$redirect = isset($_GET['redirect']) ? $_GET['redirect'] : '';

// معالجة نموذج تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    
    // التحقق من وجود ملف بيانات العملاء
    if (file_exists('customer_data.json')) {
        $customers = json_decode(file_get_contents('customer_data.json'), true);
        
        // البحث عن العميل بواسطة البريد الإلكتروني
        $found = false;
        foreach ($customers as $customer) {
            if ($customer['email'] === $email) {
                // التحقق من كلمة المرور
                if (password_verify($password, $customer['password'])) {
                    // تسجيل الدخول بنجاح
                    $_SESSION['customer_id'] = $customer['id'];
                    $_SESSION['customer_name'] = $customer['name'];
                    $_SESSION['customer_email'] = $customer['email'];
                    
                    // إعادة التوجيه بناءً على معلمة إعادة التوجيه
                    if ($redirect === 'checkout' && !empty($_SESSION['cart'])) {
                        header('Location: customer_checkout.php');
                    } else {
                        header('Location: customer_products.php');
                    }
                    exit;
                } else {
                    $error = 'كلمة المرور غير صحيحة';
                }
                $found = true;
                break;
            }
        }
        
        if (!$found) {
            $error = 'البريد الإلكتروني غير مسجل';
        }
    } else {
        $error = 'خطأ في النظام. يرجى المحاولة مرة أخرى لاحقًا';
    }
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - متجر المنتجات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 0;
            margin: 0;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem 0;
            text-align: center;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.2rem;
            margin: 0;
            font-weight: 600;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px 50px;
        }
        
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            margin-bottom: 20px;
        }
        
        .nav-links {
            display: flex;
            gap: 15px;
        }
        
        .nav-link {
            text-decoration: none;
            color: var(--dark-color);
            font-weight: 600;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .nav-link:hover {
            color: var(--secondary-color);
        }
        
        .login-container {
            max-width: 500px;
            margin: 2rem auto;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
        }
        
        .login-header {
            background-color: var(--primary-color);
            color: white;
            padding: 1.5rem;
            text-align: center;
        }
        
        .login-header h2 {
            margin: 0;
            font-size: 1.5rem;
        }
        
        .login-form {
            padding: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: var(--transition);
        }
        
        .form-group input:focus {
            border-color: var(--secondary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }
        
        .error-message {
            color: var(--danger-color);
            margin-bottom: 1rem;
            font-weight: 600;
            padding: 10px;
            background-color: rgba(231, 76, 60, 0.1);
            border-radius: var(--border-radius);
            display: <?php echo !empty($error) ? 'block' : 'none'; ?>;
        }
        
        .login-button {
            background-color: var(--secondary-color);
            color: white;
            border: none;
            padding: 12px;
            width: 100%;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: var(--transition);
        }
        
        .login-button:hover {
            background-color: #2980b9;
        }
        
        .register-link {
            text-align: center;
            margin-top: 1.5rem;
        }
        
        .register-link a {
            color: var(--secondary-color);
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
        }
        
        .register-link a:hover {
            color: var(--primary-color);
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            text-align: center;
            padding: 1.5rem 0;
            margin-top: 3rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>متجر المنتجات</h1>
    </div>
    
    <div class="container">
        <div class="navbar">
            <div class="nav-links">
                <a href="customer_products.php" class="nav-link"><i class="fas fa-home"></i> الرئيسية</a>
            </div>
            
            <a href="customer_cart.php" class="nav-link">
                <i class="fas fa-shopping-cart fa-lg"></i>
                <?php if (!empty($_SESSION['cart'])): ?>
                    <span class="cart-count"><?php echo count($_SESSION['cart']); ?></span>
                <?php endif; ?>
            </a>
        </div>
        
        <div class="login-container">
            <div class="login-header">
                <h2><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</h2>
            </div>
            
            <div class="login-form">
                <div class="error-message"><?php echo $error; ?></div>
                
                <form method="post" action="">
                    <div class="form-group">
                        <label for="email">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">كلمة المرور</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    
                    <button type="submit" class="login-button">تسجيل الدخول</button>
                </form>
                
                <div class="register-link">
                    ليس لديك حساب؟ <a href="customer_register.php<?php echo $redirect ? '?redirect='.$redirect : ''; ?>">إنشاء حساب جديد</a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>جميع الحقوق محفوظة &copy; <?php echo date('Y'); ?> متجر المنتجات</p>
    </div>
</body>
</html>