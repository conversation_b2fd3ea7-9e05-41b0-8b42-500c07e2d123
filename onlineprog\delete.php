<?php
// ملف تخزين بيانات المنتجات
$dataFile = 'products_data.json';
$uploadsDir = 'uploads/';

// التحقق من وجود معرف المنتج
if (!isset($_GET['id'])) {
    header('Location: index.php');
    exit;
}

$id = (int)$_GET['id'];

// تحميل البيانات الحالية
if (file_exists($dataFile)) {
    $productsData = json_decode(file_get_contents($dataFile), true);
    
    // التحقق من وجود المنتج
    if (isset($productsData[$id])) {
        // حذف الصورة من المجلد
        $imagePath = $uploadsDir . $productsData[$id]['image'];
        if (file_exists($imagePath)) {
            unlink($imagePath);
        }
        
        // حذف المنتج من المصفوفة
        array_splice($productsData, $id, 1);
        
        // حفظ البيانات المحدثة
        file_put_contents($dataFile, json_encode($productsData, JSON_PRETTY_PRINT));
    }
}

// إعادة التوجيه إلى الصفحة الرئيسية
header('Location: index.php');
exit;
?>