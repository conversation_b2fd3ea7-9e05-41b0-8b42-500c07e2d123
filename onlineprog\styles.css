:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
    padding-bottom: 70px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 0.8rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-logo h1 {
    font-size: 1.5rem;
    margin: 0;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Search Box */
.search-box-container {
    margin-bottom: 20px;
}

.search-form {
    display: flex;
    gap: 10px;
}

.search-input-container {
    flex: 1;
}

.search-input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-family: 'Cairo', sans-serif;
}

.search-button {
    background-color: var(--secondary-color);
    color: white;
    border: none;
    padding: 0 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
}

/* Categories Grid */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.category-item {
    text-decoration: none;
    color: var(--dark-color);
    text-align: center;
}

.category-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 10px;
    background-color: var(--light-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--secondary-color);
}

.category-name {
    font-size: 0.9rem;
    font-weight: 500;
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 20px;
}

.product-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 10px;
    box-shadow: var(--box-shadow);
}

.product-image {
    width: 100%;
    height: 160px;
    object-fit: contain;
    margin-bottom: 10px;
}

.product-title {
    font-size: 1rem;
    margin-bottom: 8px;
}

.product-price {
    color: var(--accent-color);
    font-weight: 600;
    font-size: 1.1rem;
}

/* Bottom Navigation */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 10px;
    z-index: 1000;
}

.nav-item {
    text-decoration: none;
    color: #666;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 0.8rem;
}

.nav-item.active {
    color: var(--secondary-color);
}

.nav-center-btn {
    position: relative;
    top: -20px;
}

.center-button {
    width: 50px;
    height: 50px;
    background: var(--secondary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .header-logo h1 {
        font-size: 1.2rem;
    }
    
    .categories-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .container {
        padding: 10px;
    }
    
    .category-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .category-name {
        font-size: 0.8rem;
    }
}