<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عروض الأسعار - نظام إدارة المنتجات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- إضافة مكتبة JsBarcode لإنشاء الباركود -->
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 0;
            margin: 0;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem 0;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.2rem;
            margin: 0;
            font-weight: 600;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px 50px;
        }
        
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .card-header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem 1.5rem;
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .card-header i {
            margin-left: 10px;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1.2rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        input[type="text"], 
        input[type="number"], 
        input[type="file"],
        select {
            width: 100%;
            padding: 0.8rem 1rem;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: var(--transition);
        }
        
        input[type="text"]:focus, 
        input[type="number"]:focus,
        select:focus {
            border-color: var(--secondary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }
        
        .btn {
            display: inline-block;
            background-color: var(--secondary-color);
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            text-align: center;
            transition: var(--transition);
            text-decoration: none;
        }
        
        .btn:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background-color: var(--secondary-color);
        }
        
        .btn-success {
            background-color: var(--success-color);
        }
        
        .btn-danger {
            background-color: var(--danger-color);
        }
        
        .btn-warning {
            background-color: var(--warning-color);
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
        
        .product-form {
            background-color: #f8f9fa;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
        }
        
        .product-list {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .product-list th, .product-list td {
            padding: 0.8rem;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }
        
        .product-list th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        
        .product-list tr:hover {
            background-color: #f8f9fa;
        }
        
        .empty-products {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
            font-size: 1.1rem;
        }
        
        .action-buttons-top {
            display: flex;
            justify-content: flex-start;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .action-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .totals {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .total-item {
            padding: 1.5rem;
            border-radius: var(--border-radius);
            text-align: center;
            color: white;
        }
        
        .total-item h3 {
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
        }
        
        .total-item p {
            font-size: 1.8rem;
            font-weight: 600;
            margin: 0;
        }
        
        .total-cost {
            background-color: var(--primary-color);
        }
        
        .total-selling {
            background-color: var(--secondary-color);
        }
        
        .total-profit {
            background-color: var(--success-color);
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            text-align: center;
            padding: 1.5rem 0;
            margin-top: 3rem;
        }
        
        /* منطقة السحب والإفلات */
        .drop-zone {
            border: 3px dashed #ccc;
            border-radius: var(--border-radius);
            padding: 2rem;
            text-align: center;
            margin-bottom: 1.5rem;
            background-color: #f8f9fa;
            cursor: pointer;
        }
        
        .drop-zone.active {
            border-color: var(--secondary-color);
            background-color: rgba(52, 152, 219, 0.1);
        }
        
        .drop-zone p {
            margin: 0;
            color: #6c757d;
        }
        
        .drop-zone i {
            font-size: 2rem;
            color: #6c757d;
            margin-bottom: 1rem;
        }
        
        .preview-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .image-preview {
            background-color: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
            padding: 0.8rem;
            position: relative;
        }
        
        .image-preview img {
            width: 100%;
            height: 100px;
            object-fit: cover;
            border-radius: calc(var(--border-radius) - 4px);
            margin-bottom: 0.8rem;
        }
        
        .image-preview input {
            margin-bottom: 0.8rem;
            font-size: 0.9rem;
        }
        
        .image-preview .btn-add-to-order {
            width: 100%;
            padding: 0.5rem;
            font-size: 0.8rem;
        }
        
        .image-preview .btn-remove {
            position: absolute;
            top: 5px;
            right: 5px;
            background-color: rgba(231, 76, 60, 0.8);
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        /* أنماط للباركود */
        .barcode-container {
            background-color: white;
            padding: 5px;
            border-radius: var(--border-radius);
            text-align: center;
            margin-bottom: 10px;
        }
        
        .barcode {
            width: 100%;
            max-height: 50px;
        }
        
        @media print {
            .barcode-container {
                page-break-inside: avoid;
            }
        }
        /* أنماط للطباعة */
        @media print {
            .header-buttons, .footer, .product-form, .drop-zone, .action-buttons-top, .action-buttons {
                display: none !important;
            }
            
            .header {
                background-color: white !important;
                color: black !important;
                padding: 10px 0 !important;
                margin-bottom: 20px !important;
                box-shadow: none !important;
            }
            
            body {
                background-color: white !important;
            }
            
            .card {
                box-shadow: none !important;
                margin-bottom: 10px !important;
            }
            
            .card-header {
                background-color: white !important;
                color: black !important;
                border-bottom: 1px solid #eee !important;
            }
            
            .product-list, .totals {
                page-break-inside: avoid;
            }
            
            .product-list th, .product-list td {
                border-color: #000;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>عروض الأسعار</h1>
    </div>
    
    <div class="container">
        <div class="card">
            <div class="card-header">
                <span><i class="fas fa-tags"></i> إنشاء عرض سعر جديد</span>
                <a href="index.php" class="btn btn-sm btn-primary"><i class="fas fa-home"></i> الرئيسية</a>
            </div>
            <div class="card-body">
                <!-- نموذج إضافة منتج -->
                <form id="add-product-form" class="product-form">
                    <div class="form-group">
                        <label for="barcode">الباركود</label>
                        <input type="text" id="barcode" name="barcode" placeholder="أدخل الباركود أو اتركه فارغًا للإنشاء التلقائي">
                    </div>
                    <div class="form-group">
                        <label for="product_name">اسم المنتج</label>
                        <input type="text" id="product_name" name="product_name" placeholder="أدخل اسم المنتج" required>
                    </div>
                    <div class="form-group">
                        <label for="cost_price">سعر التكلفة</label>
                        <input type="number" id="cost_price" name="cost_price" step="0.01" min="0" placeholder="0.00" required>
                    </div>
                    <div class="form-group">
                        <label for="selling_price">سعر البيع</label>
                        <input type="number" id="selling_price" name="selling_price" step="0.01" min="0" placeholder="0.00" required>
                    </div>
                    <div class="form-group">
                        <label for="quantity">الكمية</label>
                        <input type="number" id="quantity" name="quantity" min="1" value="1" required>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-success"><i class="fas fa-plus"></i> إضافة للعرض</button>
                    </div>
                </form>
                
                <!-- منطقة السحب والإفلات -->
                <div class="drop-zone" id="drop-zone">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>اسحب وأفلت صور المنتجات هنا أو انقر للاختيار</p>
                    <input type="file" id="fileElem" accept="image/*" multiple style="display:none">
                </div>
                
                <!-- منطقة عرض الصور -->
                <div id="preview-container" class="preview-container"></div>
                
                <!-- أزرار الإجراءات العلوية -->
                <div class="action-buttons-top">
                    <button id="clear-all" class="btn btn-danger"><i class="fas fa-trash-alt"></i> مسح الكل</button>
                    <button id="cancel" class="btn btn-warning"><i class="fas fa-times"></i> إلغاء</button>
                </div>
                
                <!-- قائمة المنتجات -->
                <div id="product-list-container">
                    <table class="product-list" id="product-list">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الباركود</th>
                                <th>اسم المنتج</th>
                                <th>سعر التكلفة</th>
                                <th>سعر البيع</th>
                                <th>الكمية</th>
                                <th>إجمالي التكلفة</th>
                                <th>إجمالي البيع</th>
                                <th>الربح</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="product-items">
                            <!-- سيتم إضافة المنتجات هنا بواسطة JavaScript -->
                        </tbody>
                    </table>
                    <div id="empty-products" class="empty-products">
                        لا توجد منتجات في عرض السعر حاليًا
                    </div>
                </div>
                
                <!-- إجماليات العرض -->
                <div class="totals">
                    <div class="total-item total-cost">
                        <h3>إجمالي التكلفة</h3>
                        <p id="total-cost">0.00</p>
                    </div>
                    <div class="total-item total-selling">
                        <h3>إجمالي البيع</h3>
                        <p id="total-selling">0.00</p>
                    </div>
                    <div class="total-item total-profit">
                        <h3>إجمالي الربح المتوقع</h3>
                        <p id="total-profit">0.00</p>
                    </div>
                </div>
                
                <!-- أزرار الإجراءات -->
                <div class="action-buttons">
                    <button id="print-offer" class="btn btn-primary"><i class="fas fa-print"></i> طباعة العرض</button>
                    <button id="save-offer" class="btn btn-success"><i class="fas fa-save"></i> حفظ العرض</button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>جميع الحقوق محفوظة &copy; <?php echo date('Y'); ?> نظام إدارة المنتجات</p>
    </div>

    <script>
        // انتظار تحميل الصفحة بالكامل
        document.addEventListener('DOMContentLoaded', function() {
            // تحديد العناصر
            const addProductForm = document.getElementById('add-product-form');
            const productItems = document.getElementById('product-items');
            const emptyProducts = document.getElementById('empty-products');
            const productList = document.getElementById('product-list');
            const totalCost = document.getElementById('total-cost');
            const totalSelling = document.getElementById('total-selling');
            const totalProfit = document.getElementById('total-profit');
            const clearAllBtn = document.getElementById('clear-all');
            const cancelBtn = document.getElementById('cancel');
            const printOfferBtn = document.getElementById('print-offer');
            const saveOfferBtn = document.getElementById('save-offer');
            const dropZone = document.getElementById('drop-zone');
            
            // منطقة السحب والإفلات
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, preventDefaults, false);
            });
            
            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            ['dragenter', 'dragover'].forEach(eventName => {
                dropZone.addEventListener(eventName, highlight, false);
            });
            
            ['dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, unhighlight, false);
            });
            
            function highlight() {
                dropZone.classList.add('active');
            }
            
            function unhighlight() {
                dropZone.classList.remove('active');
            }
            
            dropZone.addEventListener('drop', handleDrop, false);
            dropZone.addEventListener('click', () => document.getElementById('fileElem').click(), false);
            document.getElementById('fileElem').addEventListener('change', function() {
                handleFiles(this.files);
            });
            
            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;
                handleFiles(files);
            }
            
            function handleFiles(files) {
                const previewContainer = document.getElementById('preview-container');
                
                if (files.length > 0) {
                    Array.from(files).forEach((file, index) => {
                        if (file.type.startsWith('image/')) {
                            const reader = new FileReader();
                            reader.onload = function(e) {
                                const preview = document.createElement('div');
                                preview.className = 'image-preview';
                                preview.innerHTML = `
                                    <button type="button" class="btn-remove"><i class="fas fa-times"></i></button>
                                    <img src="${e.target.result}" alt="Preview">
                                    <input type="text" class="product-name" placeholder="اسم المنتج" required>
                                    <input type="number" class="cost-price" placeholder="سعر التكلفة" step="0.01" min="0" required>
                                    <input type="number" class="selling-price" placeholder="سعر البيع" step="0.01" min="0" required>
                                    <input type="number" class="quantity" placeholder="الكمية" min="1" value="1" required>
                                    <div class="barcode-container" style="margin-top: 8px; margin-bottom: 8px;">
                                        <svg class="barcode"></svg>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-success btn-add-to-order">إضافة للعرض</button>
                                `;
                                previewContainer.appendChild(preview);
                                
                                // إنشاء باركود للمنتج
                                const barcodeValue = generateBarcode();
                                JsBarcode(preview.querySelector('.barcode'), barcodeValue, {
                                    format: "EAN13",
                                    lineColor: "#000",
                                    width: 2,
                                    height: 40,
                                    displayValue: true
                                });
                                
                                // إضافة مستمع حدث لزر الإضافة للعرض
                                const addButton = preview.querySelector('.btn-add-to-order');
                                addButton.addEventListener('click', function() {
                                    const nameInput = preview.querySelector('.product-name');
                                    const costInput = preview.querySelector('.cost-price');
                                    const sellingInput = preview.querySelector('.selling-price');
                                    const quantityInput = preview.querySelector('.quantity');
                                    
                                    const name = nameInput.value;
                                    const costPrice = parseFloat(costInput.value);
                                    const sellingPrice = parseFloat(sellingInput.value);
                                    const quantity = parseInt(quantityInput.value) || 1;
                                    
                                    if (name && !isNaN(costPrice) && !isNaN(sellingPrice)) {
                                        const product = {
                                            id: Date.now(),
                                            barcode: barcodeValue,
                                            name: name,
                                            costPrice: costPrice,
                                            sellingPrice: sellingPrice,
                                            quantity: quantity,
                                            totalCost: costPrice * quantity,
                                            totalSelling: sellingPrice * quantity,
                                            profit: (sellingPrice - costPrice) * quantity,
                                            image: e.target.result
                                        };
                                        
                                        products.push(product);
                                        saveProducts();
                                        renderProducts();
                                        updateTotals();
                                        
                                        // إزالة العنصر بعد إضافته للعرض
                                        preview.remove();
                                    } else {
                                        alert('الرجاء ملء جميع الحقول المطلوبة بشكل صحيح');
                                    }
                                });
                                
                                // إضافة مستمع حدث لزر الإزالة
                                preview.querySelector('.btn-remove').addEventListener('click', function() {
                                    preview.remove();
                                });
                            };
                            reader.readAsDataURL(file);
                        }
                    });
                }
            }
            
            // مصفوفة لتخزين المنتجات
            let products = [];
            
            // استرجاع المنتجات من التخزين المحلي إذا كانت موجودة
            if (localStorage.getItem('priceOfferProducts')) {
                products = JSON.parse(localStorage.getItem('priceOfferProducts'));
                renderProducts();
                updateTotals();
            }
            
            // دالة لإنشاء باركود تلقائي
            function generateBarcode() {
                // إنشاء باركود من 12 رقم (لمعيار EAN-13، الرقم الـ 13 هو رقم التحقق)
                let barcode = '';
                // إضافة رقم البلد (مثلاً 6 للسعودية)
                barcode += '6';
                // إضافة 11 رقم عشوائي
                for (let i = 0; i < 11; i++) {
                    barcode += Math.floor(Math.random() * 10);
                }
                
                // حساب رقم التحقق (check digit) لمعيار EAN-13
                let sum = 0;
                for (let i = 0; i < 12; i++) {
                    sum += parseInt(barcode[i]) * (i % 2 === 0 ? 1 : 3);
                }
                let checkDigit = (10 - (sum % 10)) % 10;
                
                // إضافة رقم التحقق للباركود
                return barcode + checkDigit;
            }
            
            // إضافة منتج جديد
            addProductForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const barcode = document.getElementById('barcode').value;
                const name = document.getElementById('product_name').value;
                const costPrice = parseFloat(document.getElementById('cost_price').value);
                const sellingPrice = parseFloat(document.getElementById('selling_price').value);
                const quantity = parseInt(document.getElementById('quantity').value) || 1;
                
                if (name && !isNaN(costPrice) && !isNaN(sellingPrice)) {
                    const product = {
                        id: Date.now(),
                        barcode: barcode || generateBarcode(),
                        name: name,
                        costPrice: costPrice,
                        sellingPrice: sellingPrice,
                        quantity: quantity,
                        totalCost: costPrice * quantity,
                        totalSelling: sellingPrice * quantity,
                        profit: (sellingPrice - costPrice) * quantity
                    };
                    
                    products.push(product);
                    saveProducts();
                    renderProducts();
                    updateTotals();
                    
                    // إعادة تعيين النموذج
                    addProductForm.reset();
                    document.getElementById('barcode').focus();
                }
            });
            
            // حذف منتج
            function deleteProduct(id) {
                products = products.filter(product => product.id !== id);
                saveProducts();
                renderProducts();
                updateTotals();
            }
            
            // مسح جميع المنتجات
            clearAllBtn.addEventListener('click', function() {
                if (products.length === 0) {
                    alert('لا توجد منتجات لمسحها!');
                    return;
                }
                
                if (confirm('هل أنت متأكد من مسح جميع المنتجات من العرض؟')) {
                    products = [];
                    saveProducts();
                    renderProducts();
                    updateTotals();
                }
            });
            
            // إلغاء وإعادة توجيه للصفحة الرئيسية
            cancelBtn.addEventListener('click', function() {
                if (products.length > 0) {
                    if (confirm('هل أنت متأكد من إلغاء العرض؟ سيتم فقدان جميع البيانات غير المحفوظة.')) {
                        window.location.href = 'index.php';
                    }
                } else {
                    window.location.href = 'index.php';
                }
            });
            
            // طباعة العرض
            printOfferBtn.addEventListener('click', function() {
                if (products.length === 0) {
                    alert('لا يمكن طباعة عرض فارغ. الرجاء إضافة منتجات أولاً.');
                    return;
                }
                
                window.print();
            });
            
            // حفظ العرض
            saveOfferBtn.addEventListener('click', function() {
                if (products.length === 0) {
                    alert('لا يمكن حفظ عرض فارغ. الرجاء إضافة منتجات أولاً.');
                    return;
                }
                
                // تجميع بيانات العرض
                const offerData = {
                    products: products,
                    total_cost: parseFloat(totalCost.textContent),
                    total_selling: parseFloat(totalSelling.textContent),
                    total_profit: parseFloat(totalProfit.textContent)
                };
                
                // إرسال البيانات إلى الخادم
                fetch('save_price_offer.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'offer_data=' + encodeURIComponent(JSON.stringify(offerData))
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('فشل الاتصال بالخادم. الرمز: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        alert('تم حفظ عرض السعر بنجاح! رقم العرض: ' + data.offer_id);
                        // إعادة تعيين العرض بعد الحفظ
                        products = [];
                        saveProducts();
                        renderProducts();
                        updateTotals();
                    } else {
                        alert('حدث خطأ أثناء حفظ العرض: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('خطأ:', error);
                    alert('حدث خطأ أثناء حفظ العرض: ' + error.message);
                });
            });
            
            // عرض المنتجات
            function renderProducts() {
                if (products.length === 0) {
                    productList.style.display = 'none';
                    emptyProducts.style.display = 'block';
                } else {
                    productList.style.display = 'table';
                    emptyProducts.style.display = 'none';
                    
                    // مسح جميع المنتجات الحالية
                    productItems.innerHTML = '';
                    
                    // إضافة المنتجات
                    products.forEach((product, index) => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${index + 1}</td>
                            <td>
                                <svg class="barcode-${product.id}"></svg>
                            </td>
                            <td>
                                ${product.image ? `<img src="${product.image}" alt="${product.name}" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px; pointer-events: none; margin-left: 8px;">` : ''}
                                <span style="pointer-events: none;">${product.name}</span>
                            </td>
                            <td>${product.costPrice.toFixed(2)}</td>
                            <td>${product.sellingPrice.toFixed(2)}</td>
                            <td>${product.quantity}</td>
                            <td>${product.totalCost.toFixed(2)}</td>
                            <td>${product.totalSelling.toFixed(2)}</td>
                            <td>${product.profit.toFixed(2)}</td>
                            <td>
                                <button class="btn btn-sm btn-danger btn-delete" data-id="${product.id}">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </td>
                        `;
                        productItems.appendChild(row);
                        
                        // إنشاء الباركود لكل منتج في القائمة
                        JsBarcode(`.barcode-${product.id}`, product.barcode, {
                            format: "EAN13",
                            lineColor: "#000",
                            width: 1.5,
                            height: 30,
                            displayValue: true,
                            fontSize: 10
                        });
                    });
                    
                    // إضافة مستمعي الأحداث لأزرار الحذف
                    document.querySelectorAll('.btn-delete').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const id = parseInt(this.getAttribute('data-id'));
                            deleteProduct(id);
                        });
                    });
                }
            }
            
            // تحديث الإجماليات
            function updateTotals() {
                let totalCostValue = 0;
                let totalSellingValue = 0;
                let totalProfitValue = 0;
                
                products.forEach(product => {
                    totalCostValue += product.totalCost;
                    totalSellingValue += product.totalSelling;
                    totalProfitValue += product.profit;
                });
                
                totalCost.textContent = totalCostValue.toFixed(2);
                totalSelling.textContent = totalSellingValue.toFixed(2);
                totalProfit.textContent = totalProfitValue.toFixed(2);
            }
            
            // حفظ المنتجات في التخزين المحلي
            function saveProducts() {
                localStorage.setItem('priceOfferProducts', JSON.stringify(products));
            }
            
            // التركيز على حقل الباركود عند تحميل الصفحة
            window.onload = function() {
                document.getElementById('barcode').focus();
            };
        });
    </script>
</body>
</html>