<?php
session_start();

// التحقق مما إذا كان المستخدم مسجل الدخول بالفعل
if (isset($_SESSION['customer_id'])) {
    // إعادة التوجيه إلى الصفحة الرئيسية
    header('Location: customer_products.php');
    exit;
}

$error = '';
$success = '';
$redirect = isset($_GET['redirect']) ? $_GET['redirect'] : '';

// معالجة نموذج التسجيل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $phone = trim($_POST['phone']);
    $address = trim($_POST['address']);
    
    // التحقق من تطابق كلمات المرور
    if ($password !== $confirm_password) {
        $error = 'كلمات المرور غير متطابقة';
    } else {
        // التحقق من وجود ملف بيانات العملاء
        $customers = [];
        if (file_exists('customer_data.json')) {
            $customers = json_decode(file_get_contents('customer_data.json'), true);
        }
        
        // التحقق من عدم وجود بريد إلكتروني مكرر
        $email_exists = false;
        foreach ($customers as $customer) {
            if ($customer['email'] === $email) {
                $email_exists = true;
                break;
            }
        }
        
        if ($email_exists) {
            $error = 'البريد الإلكتروني مسجل بالفعل';
        } else {
            // إنشاء معرف فريد للعميل
            $customer_id = time() . rand(1000, 9999);
            
            // تشفير كلمة المرور
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            // إنشاء سجل العميل الجديد
            $new_customer = [
                'id' => $customer_id,
                'name' => $name,
                'email' => $email,
                'password' => $hashed_password,
                'phone' => $phone,
                'address' => $address,
                'registration_date' => date('Y-m-d H:i:s')
            ];
            
            // إضافة العميل إلى المصفوفة
            $customers[] = $new_customer;
            
            // حفظ البيانات في الملف
            if (file_put_contents('customer_data.json', json_encode($customers, JSON_PRETTY_PRINT))) {
                // تسجيل الدخول تلقائيًا بعد التسجيل
                $_SESSION['customer_id'] = $customer_id;
                $_SESSION['customer_name'] = $name;
                $_SESSION['customer_email'] = $email;
                
                // إعادة التوجيه بناءً على معلمة إعادة التوجيه
                if ($redirect === 'checkout' && !empty($_SESSION['cart'])) {
                    header('Location: customer_checkout.php');
                } else {
                    header('Location: customer_products.php');
                }
                exit;
            } else {
                $error = 'حدث خطأ أثناء تسجيل الحساب. يرجى المحاولة مرة أخرى';
            }
        }
    }
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - متجر المنتجات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 0;
            margin: 0;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem 0;
            text-align: center;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.2rem;
            margin: 0;
            font-weight: 600;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px 50px;
        }
        
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            margin-bottom: 20px;
        }
        
        .nav-links {
            display: flex;
            gap: 15px;
        }
        
        .nav-link {
            text-decoration: none;
            color: var(--dark-color);
            font-weight: 600;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .nav-link:hover {
            color: var(--secondary-color);
        }
        
        .register-container {
            max-width: 600px;
            margin: 2rem auto;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
        }
        
        .register-header {
            background-color: var(--primary-color);
            color: white;
            padding: 1.5rem;
            text-align: center;
        }
        
        .register-header h2 {
            margin: 0;
            font-size: 1.5rem;
        }
        
        .register-form {
            padding: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: var(--transition);
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .form-group input:focus, .form-group textarea:focus {
            border-color: var(--secondary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }
        
        .error-message {
            color: var(--danger-color);
            margin-bottom: 1rem;
            font-weight: 600;
            padding: 10px;
            background-color: rgba(231, 76, 60, 0.1);
            border-radius: var(--border-radius);
            display: <?php echo !empty($error) ? 'block' : 'none'; ?>;
        }
        
        .success-message {
            color: var(--success-color);
            margin-bottom: 1rem;
            font-weight: 600;
            padding: 10px;
            background-color: rgba(46, 204, 113, 0.1);
            border-radius: var(--border-radius);
            display: <?php echo !empty($success) ? 'block' : 'none'; ?>;
        }
        
        .register-button {
            background-color: var(--secondary-color);
            color: white;
            border: none;
            padding: 12px;
            width: 100%;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: var(--transition);
        }
        
        .register-button:hover {
            background-color: #2980b9;
        }
        
        .login-link {
            text-align: center;
            margin-top: 1.5rem;
        }
        
        .login-link a {
            color: var(--secondary-color);
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
        }
        
        .login-link a:hover {
            color: var(--primary-color);
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            text-align: center;
            padding: 1.5rem 0;
            margin-top: 3rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>متجر المنتجات</h1>
    </div>
    
    <div class="container">
        <div class="navbar">
            <div class="nav-links">
                <a href="customer_products.php" class="nav-link"><i class="fas fa-home"></i> الرئيسية</a>
            </div>
            
            <a href="customer_cart.php" class="nav-link">
                <i class="fas fa-shopping-cart fa-lg"></i>
                <?php if (!empty($_SESSION['cart'])): ?>
                    <span class="cart-count"><?php echo count($_SESSION['cart']); ?></span>
                <?php endif; ?>
            </a>
        </div>
        
        <div class="register-container">
            <div class="register-header">
                <h2><i class="fas fa-user-plus"></i> إنشاء حساب جديد</h2>
            </div>
            
            <div class="register-form">
                <div class="error-message"><?php echo $error; ?></div>
                <div class="success-message"><?php echo $success; ?></div>
                
                <form method="post" action="">
                    <div class="form-group">
                        <label for="name">الاسم الكامل</label>
                        <input type="text" id="name" name="name" required value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="email">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" required value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="password">كلمة المرور</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm_password">تأكيد كلمة المرور</label>
                        <input type="password" id="confirm_password" name="confirm_password" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">رقم الهاتف</label>
                        <input type="tel" id="phone" name="phone" required value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="address">العنوان</label>
                        <textarea id="address" name="address" required><?php echo isset($_POST['address']) ? htmlspecialchars($_POST['address']) : ''; ?></textarea>
                    </div>
                    
                    <button type="submit" class="register-button">إنشاء حساب</button>
                </form>
                
                <div class="login-link">
                    لديك حساب بالفعل؟ <a href="customer_login.php<?php echo $redirect ? '?redirect='.$redirect : ''; ?>">تسجيل الدخول</a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>جميع الحقوق محفوظة &copy; <?php echo date('Y'); ?> متجر المنتجات</p>
    </div>
</body>
</html>