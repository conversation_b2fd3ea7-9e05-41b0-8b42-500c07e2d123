<?php
// التحقق من وجود معلمة المعرف
if (!isset($_GET['id'])) {
    header('Location: saved_price_offers.php');
    exit;
}

$offerId = $_GET['id'];
$offersFile = 'price_offers_data.json';

// التحقق من وجود ملف البيانات
if (!file_exists($offersFile)) {
    header('Location: saved_price_offers.php');
    exit;
}

// قراءة بيانات عروض الأسعار
$offersData = json_decode(file_get_contents($offersFile), true);

// البحث عن عرض السعر بواسطة المعرف
$offerFound = false;
$offerDetails = null;

foreach ($offersData as $offer) {
    if ($offer['offer_id'] == $offerId) {
        $offerDetails = $offer;
        $offerFound = true;
        break;
    }
}

// إذا لم يتم العثور على عرض السعر، إعادة التوجيه إلى صفحة عروض الأسعار المحفوظة
if (!$offerFound) {
    header('Location: saved_price_offers.php');
    exit;
}

// تعيين نوع المحتوى لملف إكسل
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment;filename="عرض_سعر_' . $offerId . '.xls"');
header('Cache-Control: max-age=0');

// إنشاء ملف إكسل بتنسيق HTML (طريقة بسيطة بدون مكتبات خارجية)
?>
<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <!--[if gte mso 9]>
    <xml>
        <x:ExcelWorkbook>
            <x:ExcelWorksheets>
                <x:ExcelWorksheet>
                    <x:Name>عرض سعر</x:Name>
                    <x:WorksheetOptions>
                        <x:DisplayRightToLeft/>
                    </x:WorksheetOptions>
                </x:ExcelWorksheet>
            </x:ExcelWorksheets>
        </x:ExcelWorkbook>
    </xml>
    <![endif]-->
    <style>
        table, th, td {
            border: 1px solid black;
            border-collapse: collapse;
            padding: 5px;
            text-align: right;
            vertical-align: middle;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .header {
            font-size: 16pt;
            font-weight: bold;
            text-align: center;
            background-color: #f2f2f2;
            padding: 10px;
        }
        .info {
            margin: 10px 0;
            font-weight: bold;
        }
        .total {
            font-weight: bold;
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="header">تفاصيل عرض السعر #<?php echo $offerDetails['offer_id']; ?></div>
    
    <div class="info">
        <p>رقم العرض: <?php echo $offerDetails['offer_id']; ?></p>
        <p>تاريخ العرض: <?php echo $offerDetails['offer_date']; ?></p>
        <p>عدد المنتجات: <?php echo count($offerDetails['products']); ?></p>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>#</th>
                <th>اسم المنتج</th>
                <th>الباركود</th>
                <th>سعر التكلفة</th>
                <th>سعر البيع</th>
                <th>الكمية</th>
                <th>الإجمالي</th>
                <!-- تم إخفاء عمود الربح -->
            </tr>
        </thead>
        <tbody>
            <?php foreach ($offerDetails['products'] as $index => $product): ?>
                <?php $totalPrice = $product['sellingPrice'] * $product['quantity']; ?>
                <tr>
                    <td><?php echo $index + 1; ?></td>
                    <td><?php echo $product['name']; ?></td>
                    <td><?php echo isset($product['barcode']) ? $product['barcode'] : ''; ?></td>
                    <td><?php echo $product['costPrice']; ?></td>
                    <td><?php echo $product['sellingPrice']; ?></td>
                    <td><?php echo $product['quantity']; ?></td>
                    <td><?php echo $totalPrice; ?></td>
                    <!-- تم إخفاء خلية الربح -->
                </tr>
            <?php endforeach; ?>
        </tbody>
        <tfoot>
            <tr class="total">
                <td colspan="3">الإجماليات</td>
                <td><?php echo $offerDetails['total_cost']; ?></td>
                <td><?php echo $offerDetails['total_selling']; ?></td>
                <td>-</td>
                <td><?php echo $offerDetails['total_selling']; ?></td>
                <!-- تم إخفاء خلية إجمالي الربح -->
            </tr>
        </tfoot>
    </table>
</body>
</html>